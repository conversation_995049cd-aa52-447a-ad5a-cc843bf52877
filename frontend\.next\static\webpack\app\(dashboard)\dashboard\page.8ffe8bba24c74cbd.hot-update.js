"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para formatar odds\n    const formatOdd = (odd)=>{\n        return odd.toFixed(2);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = Object.keys(marketOdds).length > 0;\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? Object.entries(marketOdds).map((param)=>{\n                                        let [bookmakerId, odds] = param;\n                                        // Verificar se odds é um array válido\n                                        if (!Array.isArray(odds) || odds.length === 0) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-foreground font-medium\",\n                                                        children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: marketHeaders.length - 1,\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"Dados indispon\\xedveis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, bookmakerId, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 25\n                                            }, this);\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-3 text-foreground font-medium\",\n                                                    children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 25\n                                                }, this),\n                                                odds.map((odd, oddIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                            children: formatOdd(odd)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, oddIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 27\n                                                    }, this)),\n                                                Array.from({\n                                                    length: Math.max(0, marketHeaders.length - 1 - odds.length)\n                                                }).map((_, emptyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"-\"\n                                                    }, \"empty-\".concat(emptyIndex), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            ]\n                                        }, bookmakerId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 23\n                                        }, this);\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 277,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});