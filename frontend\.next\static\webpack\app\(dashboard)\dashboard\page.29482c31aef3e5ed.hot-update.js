"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para obter logo da casa de apostas\n    const getBookmakerLogo = (name)=>{\n        const logoMap = {\n            'Betano.br': '/logos/betano.png',\n            'Estrelabet': '/logos/estrelabet.png',\n            'Superbet.br': '/logos/superbet.png',\n            'KTO.br': '/logos/kto.png',\n            'Esportivabet': '/logos/esportivabet.png',\n            'BR4Bet': '/logos/br4bet.png',\n            'BetMGM.br': '/logos/betmgm.png',\n            'bet365': '/logos/bet365.png',\n            'Pinnacle': '/logos/pinnacle.png',\n            '1xBet': '/logos/1xbet.png'\n        };\n        return logoMap[name] || null;\n    };\n    // Função para gerar iniciais da casa de apostas como fallback\n    const getBookmakerInitials = (name)=>{\n        return name.split(/[\\s\\.]/).filter((word)=>word.length > 0).map((word)=>word[0].toUpperCase()).slice(0, 2).join('');\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Linha\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = marketOdds && (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0 || typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0);\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? (()=>{\n                                        // Renderizar baseado na estrutura do mercado\n                                        if (activeMarket === \"1X2\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_x, _bookmaker_2;\n                                                const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n                                                const logoUrl = getBookmakerLogo(bookmakerName);\n                                                const initials = getBookmakerInitials(bookmakerName);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: logoUrl,\n                                                                        alt: bookmakerName,\n                                                                        width: 24,\n                                                                        height: 24,\n                                                                        className: \"rounded-sm\",\n                                                                        onError: (e)=>{\n                                                                            // Fallback para iniciais se logo não carregar\n                                                                            const target = e.target;\n                                                                            target.style.display = 'none';\n                                                                            const parent = target.parentElement;\n                                                                            if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                                                                const initialsDiv = document.createElement('div');\n                                                                                initialsDiv.className = 'bookmaker-initials w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center';\n                                                                                initialsDiv.textContent = initials;\n                                                                                parent.appendChild(initialsDiv);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 35\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\",\n                                                                        children: initials\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 35\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-foreground font-medium\",\n                                                                        children: bookmakerName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x = bookmaker.x) === null || _bookmaker_x === void 0 ? void 0 : _bookmaker_x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 27\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"OU\" && marketOdds) {\n                                            // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)\n                                            const lines = Object.keys(marketOdds).filter((key)=>key !== 'bookmakers' && key !== 'average' && key !== 'highest');\n                                            return lines.flatMap((line)=>{\n                                                const lineData = marketOdds[line];\n                                                if (!(lineData === null || lineData === void 0 ? void 0 : lineData.bookmakers)) return [];\n                                                return Object.entries(lineData.bookmakers).map((param)=>{\n                                                    let [bookmakerId, bookmaker] = param;\n                                                    var _bookmaker_over, _bookmaker_under;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-foreground font-medium\",\n                                                                children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-muted-foreground font-medium\",\n                                                                children: line\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_over = bookmaker.over) === null || _bookmaker_over === void 0 ? void 0 : _bookmaker_over.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_under = bookmaker.under) === null || _bookmaker_under === void 0 ? void 0 : _bookmaker_under.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(line, \"-\").concat(bookmakerId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                });\n                                            });\n                                        }\n                                        if (activeMarket === \"DC\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1x, _bookmaker_12, _bookmaker_x2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1x = bookmaker[\"1x\"]) === null || _bookmaker_1x === void 0 ? void 0 : _bookmaker_1x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_12 = bookmaker[\"12\"]) === null || _bookmaker_12 === void 0 ? void 0 : _bookmaker_12.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x2 = bookmaker.x2) === null || _bookmaker_x2 === void 0 ? void 0 : _bookmaker_x2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"DNB\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"BTTS\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_yes, _bookmaker_no;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_yes = bookmaker.yes) === null || _bookmaker_yes === void 0 ? void 0 : _bookmaker_yes.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_no = bookmaker.no) === null || _bookmaker_no === void 0 ? void 0 : _bookmaker_no.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        // Fallback para outros mercados\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: marketHeaders.length,\n                                                className: \"p-6 text-center text-muted-foreground\",\n                                                children: \"Estrutura de dados n\\xe3o suportada para este mercado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 23\n                                        }, this);\n                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});