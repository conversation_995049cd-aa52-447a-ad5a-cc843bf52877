{"version": 3, "file": "AstEntity.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstEntity.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;;;;;;;GAaG;AACH,MAAsB,SAAS;CAY9B;AAZD,8BAYC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAsB,kBAAmB,SAAQ,SAAS;CAAG;AAA7D,gDAA6D", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\n/**\n * `AstEntity` is the abstract base class for analyzer objects that can become a `CollectorEntity`.\n *\n * @remarks\n *\n * The subclasses are:\n * ```\n * - AstEntity\n *   - AstSymbol\n *   - AstSyntheticEntity\n *     - AstImport\n *     - AstNamespaceImport\n * ```\n */\nexport abstract class AstEntity {\n  /**\n   * The original name of the symbol, as exported from the module (i.e. source file)\n   * containing the original TypeScript definition.  Constructs such as\n   * `import { X as Y } from` may introduce other names that differ from the local name.\n   *\n   * @remarks\n   * For the most part, `localName` corresponds to `followedSymbol.name`, but there\n   * are some edge cases.  For example, the ts.Symbol.name for `export default class X { }`\n   * is actually `\"default\"`, not `\"X\"`.\n   */\n  public abstract readonly localName: string;\n}\n\n/**\n * `AstSyntheticEntity` is the abstract base class for analyzer objects whose emitted declarations\n * are not text transformations performed by the `Span` helper.\n *\n * @remarks\n * Most of API Extractor's output is produced by using the using the `Span` utility to regurgitate strings from\n * the input .d.ts files.  If we need to rename an identifier, the `Span` visitor can pick out an interesting\n * node and rewrite its string, but otherwise the transformation operates on dumb text and not compiler concepts.\n * (Historically we did this because the compiler's emitter was an internal API, but it still has some advantages,\n * for example preserving syntaxes generated by an older compiler to avoid incompatibilities.)\n *\n * This strategy does not work for cases where the output looks very different from the input.  Today these\n * cases are always kinds of `import` statements, but that may change in the future.\n */\nexport abstract class AstSyntheticEntity extends AstEntity {}\n"]}