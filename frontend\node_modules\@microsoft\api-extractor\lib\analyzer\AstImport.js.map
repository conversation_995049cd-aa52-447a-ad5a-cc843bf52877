{"version": 3, "file": "AstImport.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstImport.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAG3D,oEAA6D;AAC7D,2CAAiD;AAEjD;;GAEG;AACH,IAAY,aAyBX;AAzBD,WAAY,aAAa;IACvB;;OAEG;IACH,mEAAa,CAAA;IAEb;;OAEG;IACH,+DAAW,CAAA;IAEX;;OAEG;IACH,6DAAU,CAAA;IAEV;;OAEG;IACH,iEAAY,CAAA;IAEZ;;OAEG;IACH,6DAAU,CAAA;AACZ,CAAC,EAzBW,aAAa,6BAAb,aAAa,QAyBxB;AAkBD;;;GAGG;AACH,MAAa,SAAU,SAAQ,8BAAkB;IA+D/C,YAAmB,OAA0B;QAC3C,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAErC,sGAAsG;QACtG,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,UAAU,CAAC;QAE/C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,oBAAoB;IACpB,IAAW,SAAS;QAClB,WAAW;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,OAA0B;QAC7C,QAAQ,OAAO,CAAC,UAAU,EAAE,CAAC;YAC3B,KAAK,aAAa,CAAC,aAAa;gBAC9B,OAAO,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvD,KAAK,aAAa,CAAC,WAAW;gBAC5B,OAAO,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvD,KAAK,aAAa,CAAC,UAAU;gBAC3B,OAAO,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC;YACnC,KAAK,aAAa,CAAC,YAAY;gBAC7B,OAAO,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC;YACnC,KAAK,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9B,MAAM,MAAM,GAAW,CAAC,OAAO,CAAC,UAAU;oBACxC,CAAC,CAAC,GAAG,CAAC,2BAA2B;oBACjC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,+BAA+B;wBAChE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;gBACzB,OAAO,GAAG,OAAO,CAAC,UAAU,IAAI,MAAM,EAAE,CAAC;YAC3C,CAAC;YACD;gBACE,MAAM,IAAI,iCAAa,CAAC,uBAAuB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;CACF;AA3GD,8BA2GC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type { AstSymbol } from './AstSymbol';\nimport { InternalError } from '@rushstack/node-core-library';\nimport { AstSyntheticEntity } from './AstEntity';\n\n/**\n * Indicates the import kind for an `AstImport`.\n */\nexport enum AstImportKind {\n  /**\n   * An import statement such as `import X from \"y\";`.\n   */\n  DefaultImport,\n\n  /**\n   * An import statement such as `import { X } from \"y\";`.\n   */\n  NamedImport,\n\n  /**\n   * An import statement such as `import * as x from \"y\";`.\n   */\n  StarImport,\n\n  /**\n   * An import statement such as `import x = require(\"y\");`.\n   */\n  EqualsImport,\n\n  /**\n   * An import statement such as `interface foo { foo: import(\"bar\").a.b.c }`.\n   */\n  ImportType\n}\n\n/**\n * Constructor parameters for AstImport\n *\n * @privateRemarks\n * Our naming convention is to use I____Parameters for constructor options and\n * I____Options for general function options.  However the word \"parameters\" is\n * confusingly similar to the terminology for function parameters modeled by API Extractor,\n * so we use I____Options for both cases in this code base.\n */\nexport interface IAstImportOptions {\n  readonly importKind: AstImportKind;\n  readonly modulePath: string;\n  readonly exportName: string;\n  readonly isTypeOnly: boolean;\n}\n\n/**\n * For a symbol that was imported from an external package, this tracks the import\n * statement that was used to reach it.\n */\nexport class AstImport extends AstSyntheticEntity {\n  public readonly importKind: AstImportKind;\n\n  /**\n   * The name of the external package (and possibly module path) that this definition\n   * was imported from.\n   *\n   * Example: \"@rushstack/node-core-library/lib/FileSystem\"\n   */\n  public readonly modulePath: string;\n\n  /**\n   * The name of the symbol being imported.\n   *\n   * @remarks\n   *\n   * The name depends on the type of import:\n   *\n   * ```ts\n   * // For AstImportKind.DefaultImport style, exportName would be \"X\" in this example:\n   * import X from \"y\";\n   *\n   * // For AstImportKind.NamedImport style, exportName would be \"X\" in this example:\n   * import { X } from \"y\";\n   *\n   * // For AstImportKind.StarImport style, exportName would be \"x\" in this example:\n   * import * as x from \"y\";\n   *\n   * // For AstImportKind.EqualsImport style, exportName would be \"x\" in this example:\n   * import x = require(\"y\");\n   *\n   * // For AstImportKind.ImportType style, exportName would be \"a.b.c\" in this example:\n   * interface foo { foo: import('bar').a.b.c };\n   * ```\n   */\n  public readonly exportName: string;\n\n  /**\n   * Whether it is a type-only import, for example:\n   *\n   * ```ts\n   * import type { X } from \"y\";\n   * ```\n   *\n   * This is set to true ONLY if the type-only form is used in *every* reference to this AstImport.\n   */\n  public isTypeOnlyEverywhere: boolean;\n\n  /**\n   * If this import statement refers to an API from an external package that is tracked by API Extractor\n   * (according to `PackageMetadataManager.isAedocSupportedFor()`), then this property will return the\n   * corresponding AstSymbol.  Otherwise, it is undefined.\n   */\n  public astSymbol: AstSymbol | undefined;\n\n  /**\n   * If modulePath and exportName are defined, then this is a dictionary key\n   * that combines them with a colon (\":\").\n   *\n   * Example: \"@rushstack/node-core-library/lib/FileSystem:FileSystem\"\n   */\n  public readonly key: string;\n\n  public constructor(options: IAstImportOptions) {\n    super();\n\n    this.importKind = options.importKind;\n    this.modulePath = options.modulePath;\n    this.exportName = options.exportName;\n\n    // We start with this assumption, but it may get changed later if non-type-only import is encountered.\n    this.isTypeOnlyEverywhere = options.isTypeOnly;\n\n    this.key = AstImport.getKey(options);\n  }\n\n  /** {@inheritdoc} */\n  public get localName(): string {\n    // abstract\n    return this.exportName;\n  }\n\n  /**\n   * Calculates the lookup key used with `AstImport.key`\n   */\n  public static getKey(options: IAstImportOptions): string {\n    switch (options.importKind) {\n      case AstImportKind.DefaultImport:\n        return `${options.modulePath}:${options.exportName}`;\n      case AstImportKind.NamedImport:\n        return `${options.modulePath}:${options.exportName}`;\n      case AstImportKind.StarImport:\n        return `${options.modulePath}:*`;\n      case AstImportKind.EqualsImport:\n        return `${options.modulePath}:=`;\n      case AstImportKind.ImportType: {\n        const subKey: string = !options.exportName\n          ? '*' // Equivalent to StarImport\n          : options.exportName.includes('.') // Equivalent to a named export\n            ? options.exportName.split('.')[0]\n            : options.exportName;\n        return `${options.modulePath}:${subKey}`;\n      }\n      default:\n        throw new InternalError('Unknown AstImportKind');\n    }\n  }\n}\n"]}