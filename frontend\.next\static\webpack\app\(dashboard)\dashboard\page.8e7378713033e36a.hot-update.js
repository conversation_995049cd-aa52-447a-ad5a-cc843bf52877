"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {}\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para formatar odds\n    const formatOdd = (odd)=>{\n        return odd.toFixed(2);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) return {};\n        switch(activeMarket){\n            case \"1X2\":\n                return displayData.odds.live_odds_1x2 || {};\n            case \"OU\":\n                return displayData.odds.live_odds_ou || {};\n            case \"AH\":\n                return displayData.odds.live_odds_ah || {};\n            case \"DNB\":\n                return displayData.odds.live_odds_dnb || {};\n            case \"DC\":\n                return displayData.odds.live_odds_dc || {};\n            case \"BTTS\":\n                return displayData.odds.live_odds_btts || {};\n            default:\n                return {};\n        }\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = Object.keys(marketOdds).length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? Object.entries(marketOdds).map((param, index)=>{\n                                        let [bookmakerId, odds] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-3 text-foreground font-medium\",\n                                                    children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                odds.map((odd, oddIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                            children: formatOdd(odd)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, oddIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 25\n                                                    }, this)),\n                                                Array.from({\n                                                    length: Math.max(0, marketHeaders.length - 1 - odds.length)\n                                                }).map((_, emptyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"-\"\n                                                    }, \"empty-\".concat(emptyIndex), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            ]\n                                        }, bookmakerId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 21\n                                        }, this);\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"ODcIcioLXNH2oxosEIZ2ALNHauI=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});