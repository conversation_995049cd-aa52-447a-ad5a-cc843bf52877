"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {}\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para formatar odds\n    const formatOdd = (odd)=>{\n        return odd.toFixed(2);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) return {};\n        switch(activeMarket){\n            case \"1X2\":\n                return displayData.odds.live_odds_1x2 || {};\n            case \"OU\":\n                return displayData.odds.live_odds_ou || {};\n            case \"AH\":\n                return displayData.odds.live_odds_ah || {};\n            case \"DNB\":\n                return displayData.odds.live_odds_dnb || {};\n            case \"DC\":\n                return displayData.odds.live_odds_dc || {};\n            case \"BTTS\":\n                return displayData.odds.live_odds_btts || {};\n            default:\n                return {};\n        }\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = Object.keys(marketOdds).length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? Object.entries(marketOdds).map((param)=>{\n                                        let [bookmakerId, odds] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-3 text-foreground font-medium\",\n                                                    children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                odds.map((odd, oddIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                            children: formatOdd(odd)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, oddIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 25\n                                                    }, this)),\n                                                Array.from({\n                                                    length: Math.max(0, marketHeaders.length - 1 - odds.length)\n                                                }).map((_, emptyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"-\"\n                                                    }, \"empty-\".concat(emptyIndex), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            ]\n                                        }, bookmakerId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 21\n                                        }, this);\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"ODcIcioLXNH2oxosEIZ2ALNHauI=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});