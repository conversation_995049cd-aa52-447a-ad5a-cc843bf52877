"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {}\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para formatar odds\n    const formatOdd = (odd)=>{\n        return odd.toFixed(2);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) return {};\n        switch(activeMarket){\n            case \"1X2\":\n                return displayData.odds.live_odds_1x2 || {};\n            case \"OU\":\n                return displayData.odds.live_odds_ou || {};\n            case \"AH\":\n                return displayData.odds.live_odds_ah || {};\n            case \"DNB\":\n                return displayData.odds.live_odds_dnb || {};\n            case \"DC\":\n                return displayData.odds.live_odds_dc || {};\n            case \"BTTS\":\n                return displayData.odds.live_odds_btts || {};\n            default:\n                return {};\n        }\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = Object.keys(marketOdds).length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? Object.entries(marketOdds).map((param)=>{\n                                        let [bookmakerId, odds] = param;\n                                        // Verificar se odds é um array válido\n                                        if (!Array.isArray(odds) || odds.length === 0) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-foreground font-medium\",\n                                                        children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: marketHeaders.length - 1,\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"Dados indispon\\xedveis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, bookmakerId, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 25\n                                            }, this);\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-3 text-foreground font-medium\",\n                                                    children: \"Casa \".concat(parseInt(bookmakerId) + 1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 25\n                                                }, this),\n                                                odds.map((odd, oddIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                            children: formatOdd(odd)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, oddIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 27\n                                                    }, this)),\n                                                Array.from({\n                                                    length: Math.max(0, marketHeaders.length - 1 - odds.length)\n                                                }).map((_, emptyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 text-muted-foreground\",\n                                                        children: \"-\"\n                                                    }, \"empty-\".concat(emptyIndex), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            ]\n                                        }, bookmakerId, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 23\n                                        }, this);\n                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"ODcIcioLXNH2oxosEIZ2ALNHauI=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});