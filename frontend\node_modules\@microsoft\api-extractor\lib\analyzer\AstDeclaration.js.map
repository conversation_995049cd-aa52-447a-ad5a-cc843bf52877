{"version": 3, "file": "AstDeclaration.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstDeclaration.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AAEjC,iCAA8B;AAC9B,oEAA6D;AAY7D;;;;;;;;;;;;;;;;GAgBG;AACH,MAAa,cAAc;IAqCzB,YAAmB,OAA+B;QARlD,4FAA4F;QAC3E,sBAAiB,GAAqB,EAAE,CAAC;QAEzC,sCAAiC,GAAmB,IAAI,GAAG,EAAa,CAAC;QAE1F,gDAAgD;QACxC,oBAAe,GAA8C,SAAS,CAAC;QAG7E,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE7B,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnE,oDAAoD;QACpD,EAAE;QACF,qCAAqC;QACrC,EAAE;QACF,MAAM,eAAe,GAAmC,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClG,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,EAAE,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC5C,sCAAsC;gBACtC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,IAAW,qBAAqB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,KAAqB;QAC7C,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,IAAI,iCAAa,CAAC,qCAAqC,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,iCAAa,CAAC,gEAAgE,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,SAAiB,EAAE;QAChC,MAAM,eAAe,GAAW,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrE,IAAI,MAAM,GAAW,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,eAAe,GAAG,CAAC;QACnF,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACnC,MAAM,IAAI,YAAY,CAAC;QACzB,CAAC;QACD,MAAM,IAAI,IAAI,CAAC;QAEf,KAAK,MAAM,mBAAmB,IAAI,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,EAAE,CAAC;YAClF,MAAM,IAAI,MAAM,GAAG,UAAU,mBAAmB,CAAC,SAAS,IAAI,CAAC;QACjE,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,SAAiB,EAAE;QACpC,MAAM,IAAI,GAAS,IAAI,WAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,mBAA8B;QAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,iCAAa,CAAC,wEAAwE,CAAC,CAAC;QACpG,CAAC;QAED,KAAK,IAAI,OAAO,GAA+B,IAAI,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YACvF,0EAA0E;YAC1E,IAAI,OAAO,CAAC,iCAAiC,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBACvE,OAAO;YACT,CAAC;YACD,0CAA0C;YAC1C,IAAI,mBAAmB,KAAK,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC9C,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,CAAC,iCAAiC,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG;IACI,2BAA2B,CAAC,MAAgD;QACjF,MAAM,CAAC,IAAI,CAAC,CAAC;QACb,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,IAAY;QACtC,iCAAiC;QACjC,EAAE;QACF,mEAAmE;QACnE,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,yBAAyB;YACzB,MAAM,cAAc,GAAkC,IAAI,GAAG,EAA4B,CAAC;YAE1F,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAW,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC;gBACpD,IAAI,KAAK,GAAiC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACxB,KAAK,GAAG,EAAE,CAAC;oBACX,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACvC,CAAC;gBACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACxC,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB,CAAC,IAAmB;QACrD,uBAAuB;QACvB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,sCAAsC;YAC7E,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,oCAAoC;YACpE,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC;YACnC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;YAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,iCAAiC;YACzE,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,mCAAmC;YACtE,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC;YACxC,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;YACrC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC;YACnC,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,sDAAsD;YAC5F,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC;YACvC,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;YACrC,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,0CAA0C;YACnF,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;gBACpC,OAAO,IAAI,CAAC;YAEd,gHAAgH;YAChH,0CAA0C;YAE1C,gGAAgG;YAChG,8FAA8F;YAC9F,4FAA4F;YAC5F,gGAAgG;YAChG,0DAA0D;QAC5D,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAvPD,wCAuPC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport * as ts from 'typescript';\nimport type { AstSymbol } from './AstSymbol';\nimport { Span } from './Span';\nimport { InternalError } from '@rushstack/node-core-library';\nimport type { AstEntity } from './AstEntity';\n\n/**\n * Constructor options for AstDeclaration\n */\nexport interface IAstDeclarationOptions {\n  readonly declaration: ts.Declaration;\n  readonly astSymbol: AstSymbol;\n  readonly parent: AstDeclaration | undefined;\n}\n\n/**\n * The AstDeclaration and AstSymbol classes are API Extractor's equivalent of the compiler's\n * ts.Declaration and ts.Symbol objects.  They are created by the `AstSymbolTable` class.\n *\n * @remarks\n * The AstDeclaration represents one or more syntax components of a symbol.  Usually there is\n * only one AstDeclaration per AstSymbol, but certain TypeScript constructs can have multiple\n * declarations (e.g. overloaded functions, merged declarations, etc.).\n *\n * Because of this, the `AstDeclaration` manages the parent/child nesting hierarchy (e.g. with\n * declaration merging, each declaration has its own children) and becomes the main focus\n * of analyzing AEDoc and emitting *.d.ts files.\n *\n * The AstDeclarations correspond to items from the compiler's ts.Node hierarchy, but\n * omitting/skipping any nodes that don't match the AstDeclaration.isSupportedSyntaxKind()\n * criteria.  This simplification makes the other API Extractor stages easier to implement.\n */\nexport class AstDeclaration {\n  public readonly declaration: ts.Declaration;\n\n  public readonly astSymbol: AstSymbol;\n\n  /**\n   * The parent, if this object is nested inside another AstDeclaration.\n   */\n  public readonly parent: AstDeclaration | undefined;\n\n  /**\n   * A bit set of TypeScript modifiers such as \"private\", \"protected\", etc.\n   */\n  public readonly modifierFlags: ts.ModifierFlags;\n\n  /**\n   * Additional information that is calculated later by the `Collector`.  The actual type is `DeclarationMetadata`,\n   * but we declare it as `unknown` because consumers must obtain this object by calling\n   * `Collector.fetchDeclarationMetadata()`.\n   */\n  public declarationMetadata: unknown;\n\n  /**\n   * Additional information that is calculated later by the `Collector`.  The actual type is `ApiItemMetadata`,\n   * but we declare it as `unknown` because consumers must obtain this object by calling\n   * `Collector.fetchApiItemMetadata()`.\n   */\n  public apiItemMetadata: unknown;\n\n  // NOTE: This array becomes immutable after astSymbol.analyze() sets astSymbol.analyzed=true\n  private readonly _analyzedChildren: AstDeclaration[] = [];\n\n  private readonly _analyzedReferencedAstEntitiesSet: Set<AstEntity> = new Set<AstEntity>();\n\n  // Reverse lookup used by findChildrenWithName()\n  private _childrenByName: Map<string, AstDeclaration[]> | undefined = undefined;\n\n  public constructor(options: IAstDeclarationOptions) {\n    this.declaration = options.declaration;\n    this.astSymbol = options.astSymbol;\n    this.parent = options.parent;\n\n    this.astSymbol._notifyDeclarationAttach(this);\n\n    if (this.parent) {\n      this.parent._notifyChildAttach(this);\n    }\n\n    this.modifierFlags = ts.getCombinedModifierFlags(this.declaration);\n\n    // Check for ECMAScript private fields, for example:\n    //\n    //    class Person { #name: string; }\n    //\n    const declarationName: ts.DeclarationName | undefined = ts.getNameOfDeclaration(this.declaration);\n    if (declarationName) {\n      if (ts.isPrivateIdentifier(declarationName)) {\n        // eslint-disable-next-line no-bitwise\n        this.modifierFlags |= ts.ModifierFlags.Private;\n      }\n    }\n  }\n\n  /**\n   * Returns the children for this AstDeclaration.\n   * @remarks\n   * The collection will be empty until AstSymbol.analyzed is true.\n   */\n  public get children(): ReadonlyArray<AstDeclaration> {\n    return this.astSymbol.analyzed ? this._analyzedChildren : [];\n  }\n\n  /**\n   * Returns the AstEntity objects referenced by this node.\n   * @remarks\n   * NOTE: The collection will be empty until AstSymbol.analyzed is true.\n   *\n   * Since we assume references are always collected by a traversal starting at the\n   * root of the nesting declarations, this array omits the following items because they\n   * would be redundant:\n   * - symbols corresponding to parents of this declaration (e.g. a method that returns its own class)\n   * - symbols already listed in the referencedAstSymbols property for parents of this declaration\n   *   (e.g. a method that returns its own class's base class)\n   * - symbols that are referenced only by nested children of this declaration\n   *   (e.g. if a method returns an enum, this doesn't imply that the method's class references that enum)\n   */\n  public get referencedAstEntities(): ReadonlyArray<AstEntity> {\n    return this.astSymbol.analyzed ? [...this._analyzedReferencedAstEntitiesSet] : [];\n  }\n\n  /**\n   * This is an internal callback used when the AstSymbolTable attaches a new\n   * child AstDeclaration to this object.\n   * @internal\n   */\n  public _notifyChildAttach(child: AstDeclaration): void {\n    if (child.parent !== this) {\n      throw new InternalError('Invalid call to notifyChildAttach()');\n    }\n\n    if (this.astSymbol.analyzed) {\n      throw new InternalError('_notifyChildAttach() called after analysis is already complete');\n    }\n\n    this._analyzedChildren.push(child);\n  }\n\n  /**\n   * Returns a diagnostic dump of the tree, which reports the hierarchy of\n   * AstDefinition objects.\n   */\n  public getDump(indent: string = ''): string {\n    const declarationKind: string = ts.SyntaxKind[this.declaration.kind];\n    let result: string = indent + `+ ${this.astSymbol.localName} (${declarationKind})`;\n    if (this.astSymbol.nominalAnalysis) {\n      result += ' (nominal)';\n    }\n    result += '\\n';\n\n    for (const referencedAstEntity of this._analyzedReferencedAstEntitiesSet.values()) {\n      result += indent + `  ref: ${referencedAstEntity.localName}\\n`;\n    }\n\n    for (const child of this.children) {\n      result += child.getDump(indent + '  ');\n    }\n\n    return result;\n  }\n\n  /**\n   * Returns a diagnostic dump using Span.getDump(), which reports the detailed\n   * compiler structure.\n   */\n  public getSpanDump(indent: string = ''): string {\n    const span: Span = new Span(this.declaration);\n    return span.getDump(indent);\n  }\n\n  /**\n   * This is an internal callback used when AstSymbolTable.analyze() discovers a new\n   * type reference associated with this declaration.\n   * @internal\n   */\n  public _notifyReferencedAstEntity(referencedAstEntity: AstEntity): void {\n    if (this.astSymbol.analyzed) {\n      throw new InternalError('_notifyReferencedAstEntity() called after analysis is already complete');\n    }\n\n    for (let current: AstDeclaration | undefined = this; current; current = current.parent) {\n      // Don't add references to symbols that are already referenced by a parent\n      if (current._analyzedReferencedAstEntitiesSet.has(referencedAstEntity)) {\n        return;\n      }\n      // Don't add the symbols of parents either\n      if (referencedAstEntity === current.astSymbol) {\n        return;\n      }\n    }\n\n    this._analyzedReferencedAstEntitiesSet.add(referencedAstEntity);\n  }\n\n  /**\n   * Visits all the current declaration and all children recursively in a depth-first traversal,\n   * and performs the specified action for each one.\n   */\n  public forEachDeclarationRecursive(action: (astDeclaration: AstDeclaration) => void): void {\n    action(this);\n    for (const child of this.children) {\n      child.forEachDeclarationRecursive(action);\n    }\n  }\n\n  /**\n   * Returns the list of child declarations whose `AstSymbol.localName` matches the provided `name`.\n   *\n   * @remarks\n   * This is an efficient O(1) lookup.\n   */\n  public findChildrenWithName(name: string): ReadonlyArray<AstDeclaration> {\n    // The children property returns:\n    //\n    //    return this.astSymbol.analyzed ? this._analyzedChildren : [];\n    //\n    if (!this.astSymbol.analyzed || this._analyzedChildren.length === 0) {\n      return [];\n    }\n\n    if (this._childrenByName === undefined) {\n      // Build the lookup table\n      const childrenByName: Map<string, AstDeclaration[]> = new Map<string, AstDeclaration[]>();\n\n      for (const child of this._analyzedChildren) {\n        const childName: string = child.astSymbol.localName;\n        let array: AstDeclaration[] | undefined = childrenByName.get(childName);\n        if (array === undefined) {\n          array = [];\n          childrenByName.set(childName, array);\n        }\n        array.push(child);\n      }\n      this._childrenByName = childrenByName;\n    }\n\n    return this._childrenByName.get(name) || [];\n  }\n\n  /**\n   * This function determines which ts.Node kinds will generate an AstDeclaration.\n   * These correspond to the definitions that we can add AEDoc to.\n   */\n  public static isSupportedSyntaxKind(kind: ts.SyntaxKind): boolean {\n    // (alphabetical order)\n    switch (kind) {\n      case ts.SyntaxKind.CallSignature:\n      case ts.SyntaxKind.ClassDeclaration:\n      case ts.SyntaxKind.ConstructSignature: // Example: \"new(x: number): IMyClass\"\n      case ts.SyntaxKind.Constructor: // Example: \"constructor(x: number)\"\n      case ts.SyntaxKind.EnumDeclaration:\n      case ts.SyntaxKind.EnumMember:\n      case ts.SyntaxKind.FunctionDeclaration: // Example: \"(x: number): number\"\n      case ts.SyntaxKind.GetAccessor:\n      case ts.SyntaxKind.SetAccessor:\n      case ts.SyntaxKind.IndexSignature: // Example: \"[key: string]: string\"\n      case ts.SyntaxKind.InterfaceDeclaration:\n      case ts.SyntaxKind.MethodDeclaration:\n      case ts.SyntaxKind.MethodSignature:\n      case ts.SyntaxKind.ModuleDeclaration: // Used for both \"module\" and \"namespace\" declarations\n      case ts.SyntaxKind.PropertyDeclaration:\n      case ts.SyntaxKind.PropertySignature:\n      case ts.SyntaxKind.TypeAliasDeclaration: // Example: \"type Shape = Circle | Square\"\n      case ts.SyntaxKind.VariableDeclaration:\n        return true;\n\n      // NOTE: Prior to TypeScript 3.7, in the emitted .d.ts files, the compiler would merge a GetAccessor/SetAccessor\n      // pair into a single PropertyDeclaration.\n\n      // NOTE: In contexts where a source file is treated as a module, we do create \"nominal analysis\"\n      // AstSymbol objects corresponding to a ts.SyntaxKind.SourceFile node.  However, a source file\n      // is NOT considered a nesting structure, and it does NOT act as a root for the declarations\n      // appearing in the file.  This is because the *.d.ts generator is in the business of rolling up\n      // source files, and thus wants to ignore them in general.\n    }\n\n    return false;\n  }\n}\n"]}