"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _styles_bookmaker_logos_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/bookmaker-logos.css */ \"(app-pages-browser)/./src/styles/bookmaker-logos.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    var _this = this;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para obter informações do logo da casa de apostas usando sprite sheet\n    const getBookmakerLogoInfo = (name, id)=>{\n        // Mapeamento das casas de apostas para suas posições na sprite sheet\n        // Baseado nos IDs conhecidos das casas brasileiras\n        const spriteMap = {\n            '574': 'betano',\n            '833': 'estrelabet',\n            '933': 'superbet',\n            '935': 'kto',\n            '959': 'esportivabet',\n            '999': 'br4bet',\n            '1013': 'betmgm'\n        };\n        // Mapeamento por nome como fallback\n        const nameMap = {\n            'Betano.br': 'betano',\n            'Estrelabet': 'estrelabet',\n            'Superbet.br': 'superbet',\n            'KTO.br': 'kto',\n            'Esportivabet': 'esportivabet',\n            'BR4Bet': 'br4bet',\n            'BetMGM.br': 'betmgm'\n        };\n        // Tentar mapear por ID primeiro, depois por nome\n        const spriteKey = id && spriteMap[id] || nameMap[name];\n        if (spriteKey) {\n            return {\n                useSpriteSheet: true,\n                spritePosition: spriteKey\n            };\n        }\n        return {\n            useSpriteSheet: false,\n            fallbackUrl: undefined\n        };\n    };\n    // Função para gerar iniciais da casa de apostas como fallback\n    const getBookmakerInitials = (name)=>{\n        return name.split(/[\\s\\.]/).filter((word)=>word.length > 0).map((word)=>word[0].toUpperCase()).slice(0, 2).join('');\n    };\n    // Componente para renderizar logo da casa de apostas\n    const BookmakerLogo = (param)=>{\n        let { name, id, size = 'lg' } = param;\n        const logoInfo = getBookmakerLogoInfo(name, id);\n        const initials = getBookmakerInitials(name);\n        const iconClasses = size === 'sm' ? 'w-6 h-6' : size === 'md' ? 'w-8 h-8' : 'w-30 h-30';\n        if (logoInfo.useSpriteSheet && logoInfo.spritePosition) {\n            // Usar sprite sheet do BetExplorer\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(iconClasses, \" bg-no-repeat bg-contain rounded-sm\"),\n                style: {\n                    backgroundImage: 'url(https://www.betexplorer.com/gres/images/bookmakerlogos.png?serial=2507141204BR)',\n                    backgroundPosition: \"var(--\".concat(logoInfo.spritePosition, \"-position, 0 0)\") // CSS custom property para posição\n                },\n                title: name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this);\n        }\n        // Fallback para iniciais\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(iconClasses, \" bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\"),\n            children: initials\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    };\n    // Função helper para renderizar célula da casa de apostas\n    const renderBookmakerCell = function(bookmaker, bookmakerId) {\n        let size = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'lg';\n        const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n        const textSize = size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BookmakerLogo, {\n                        name: bookmakerName,\n                        id: bookmakerId,\n                        size: size\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-foreground font-medium \".concat(textSize),\n                        children: bookmakerName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, _this);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Linha\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = marketOdds && (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0 || typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0);\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-4 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? (()=>{\n                                        // Renderizar baseado na estrutura do mercado\n                                        if (activeMarket === \"1X2\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_x, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_x = bookmaker.x) === null || _bookmaker_x === void 0 ? void 0 : _bookmaker_x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"OU\" && marketOdds) {\n                                            // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)\n                                            const lines = Object.keys(marketOdds).filter((key)=>key !== 'bookmakers' && key !== 'average' && key !== 'highest');\n                                            return lines.flatMap((line)=>{\n                                                const lineData = marketOdds[line];\n                                                if (!(lineData === null || lineData === void 0 ? void 0 : lineData.bookmakers)) return [];\n                                                return Object.entries(lineData.bookmakers).map((param)=>{\n                                                    let [bookmakerId, bookmaker] = param;\n                                                    var _bookmaker_over, _bookmaker_under;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                        children: [\n                                                            renderBookmakerCell(bookmaker, bookmakerId, 'md'),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4 text-muted-foreground font-medium\",\n                                                                children: line\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                    children: ((_bookmaker_over = bookmaker.over) === null || _bookmaker_over === void 0 ? void 0 : _bookmaker_over.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                    children: ((_bookmaker_under = bookmaker.under) === null || _bookmaker_under === void 0 ? void 0 : _bookmaker_under.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(line, \"-\").concat(bookmakerId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                });\n                                            });\n                                        }\n                                        if (activeMarket === \"DC\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1x, _bookmaker_12, _bookmaker_x2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_1x = bookmaker[\"1x\"]) === null || _bookmaker_1x === void 0 ? void 0 : _bookmaker_1x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_12 = bookmaker[\"12\"]) === null || _bookmaker_12 === void 0 ? void 0 : _bookmaker_12.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_x2 = bookmaker.x2) === null || _bookmaker_x2 === void 0 ? void 0 : _bookmaker_x2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"DNB\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"BTTS\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_yes, _bookmaker_no;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_yes = bookmaker.yes) === null || _bookmaker_yes === void 0 ? void 0 : _bookmaker_yes.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_no = bookmaker.no) === null || _bookmaker_no === void 0 ? void 0 : _bookmaker_no.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        // Fallback para outros mercados\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: marketHeaders.length,\n                                                className: \"p-6 text-center text-muted-foreground\",\n                                                children: \"Estrutura de dados n\\xe3o suportada para este mercado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 23\n                                        }, this);\n                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 374,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 372,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});