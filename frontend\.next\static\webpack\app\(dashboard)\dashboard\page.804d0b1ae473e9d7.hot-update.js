"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    var _this = this;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para obter logo da casa de apostas\n    const getBookmakerLogo = (name)=>{\n        const logoMap = {\n            'Betano.br': '/logos/betano.png',\n            'Estrelabet': '/logos/estrelabet.png',\n            'Superbet.br': '/logos/superbet.png',\n            'KTO.br': '/logos/kto.png',\n            'Esportivabet': '/logos/esportivabet.png',\n            'BR4Bet': '/logos/br4bet.png',\n            'BetMGM.br': '/logos/betmgm.png',\n            'bet365': '/logos/bet365.png',\n            'Pinnacle': '/logos/pinnacle.png',\n            '1xBet': '/logos/1xbet.png'\n        };\n        return logoMap[name] || null;\n    };\n    // Função para gerar iniciais da casa de apostas como fallback\n    const getBookmakerInitials = (name)=>{\n        return name.split(/[\\s\\.]/).filter((word)=>word.length > 0).map((word)=>word[0].toUpperCase()).slice(0, 2).join('');\n    };\n    // Função helper para renderizar célula da casa de apostas\n    const renderBookmakerCell = function(bookmaker, bookmakerId) {\n        let size = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'md';\n        const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n        const logoUrl = getBookmakerLogo(bookmakerName);\n        const initials = getBookmakerInitials(bookmakerName);\n        const iconSize = size === 'sm' ? 20 : 24;\n        const iconClasses = size === 'sm' ? 'w-5 h-5' : 'w-6 h-6';\n        const textSize = size === 'sm' ? 'text-sm' : '';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n            className: \"p-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: logoUrl,\n                        alt: bookmakerName,\n                        width: iconSize,\n                        height: iconSize,\n                        className: \"rounded-sm\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.style.display = 'none';\n                            const parent = target.parentElement;\n                            if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                const initialsDiv = document.createElement('div');\n                                initialsDiv.className = \"bookmaker-initials \".concat(iconClasses, \" bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\");\n                                initialsDiv.textContent = initials;\n                                parent.appendChild(initialsDiv);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(iconClasses, \" bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\"),\n                        children: initials\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-foreground font-medium \".concat(textSize),\n                        children: bookmakerName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, _this);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Linha\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = marketOdds && (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0 || typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0);\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? (()=>{\n                                        // Renderizar baseado na estrutura do mercado\n                                        if (activeMarket === \"1X2\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_x, _bookmaker_2;\n                                                const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n                                                const logoUrl = getBookmakerLogo(bookmakerName);\n                                                const initials = getBookmakerInitials(bookmakerName);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: logoUrl,\n                                                                        alt: bookmakerName,\n                                                                        width: 24,\n                                                                        height: 24,\n                                                                        className: \"rounded-sm\",\n                                                                        onError: (e)=>{\n                                                                            // Fallback para iniciais se logo não carregar\n                                                                            const target = e.target;\n                                                                            target.style.display = 'none';\n                                                                            const parent = target.parentElement;\n                                                                            if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                                                                const initialsDiv = document.createElement('div');\n                                                                                initialsDiv.className = 'bookmaker-initials w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center';\n                                                                                initialsDiv.textContent = initials;\n                                                                                parent.appendChild(initialsDiv);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 35\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\",\n                                                                        children: initials\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 35\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-foreground font-medium\",\n                                                                        children: bookmakerName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x = bookmaker.x) === null || _bookmaker_x === void 0 ? void 0 : _bookmaker_x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 27\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"OU\" && marketOdds) {\n                                            // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)\n                                            const lines = Object.keys(marketOdds).filter((key)=>key !== 'bookmakers' && key !== 'average' && key !== 'highest');\n                                            return lines.flatMap((line)=>{\n                                                const lineData = marketOdds[line];\n                                                if (!(lineData === null || lineData === void 0 ? void 0 : lineData.bookmakers)) return [];\n                                                return Object.entries(lineData.bookmakers).map((param)=>{\n                                                    let [bookmakerId, bookmaker] = param;\n                                                    var _bookmaker_over, _bookmaker_under;\n                                                    const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n                                                    const logoUrl = getBookmakerLogo(bookmakerName);\n                                                    const initials = getBookmakerInitials(bookmakerName);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: logoUrl,\n                                                                            alt: bookmakerName,\n                                                                            width: 20,\n                                                                            height: 20,\n                                                                            className: \"rounded-sm\",\n                                                                            onError: (e)=>{\n                                                                                const target = e.target;\n                                                                                target.style.display = 'none';\n                                                                                const parent = target.parentElement;\n                                                                                if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                                                                    const initialsDiv = document.createElement('div');\n                                                                                    initialsDiv.className = 'bookmaker-initials w-5 h-5 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center';\n                                                                                    initialsDiv.textContent = initials;\n                                                                                    parent.appendChild(initialsDiv);\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 37\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\",\n                                                                            children: initials\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-foreground font-medium text-sm\",\n                                                                            children: bookmakerName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-muted-foreground font-medium\",\n                                                                children: line\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_over = bookmaker.over) === null || _bookmaker_over === void 0 ? void 0 : _bookmaker_over.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_under = bookmaker.under) === null || _bookmaker_under === void 0 ? void 0 : _bookmaker_under.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(line, \"-\").concat(bookmakerId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 29\n                                                    }, this);\n                                                });\n                                            });\n                                        }\n                                        if (activeMarket === \"DC\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1x, _bookmaker_12, _bookmaker_x2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1x = bookmaker[\"1x\"]) === null || _bookmaker_1x === void 0 ? void 0 : _bookmaker_1x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_12 = bookmaker[\"12\"]) === null || _bookmaker_12 === void 0 ? void 0 : _bookmaker_12.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x2 = bookmaker.x2) === null || _bookmaker_x2 === void 0 ? void 0 : _bookmaker_x2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"DNB\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"BTTS\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_yes, _bookmaker_no;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_yes = bookmaker.yes) === null || _bookmaker_yes === void 0 ? void 0 : _bookmaker_yes.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_no = bookmaker.no) === null || _bookmaker_no === void 0 ? void 0 : _bookmaker_no.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        // Fallback para outros mercados\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: marketHeaders.length,\n                                                className: \"p-6 text-center text-muted-foreground\",\n                                                children: \"Estrutura de dados n\\xe3o suportada para este mercado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 23\n                                        }, this);\n                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});