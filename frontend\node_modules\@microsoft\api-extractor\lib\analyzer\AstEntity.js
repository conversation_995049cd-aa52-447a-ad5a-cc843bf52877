"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICEN<PERSON> in the project root for license information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstSyntheticEntity = exports.AstEntity = void 0;
/**
 * `AstEntity` is the abstract base class for analyzer objects that can become a `CollectorEntity`.
 *
 * @remarks
 *
 * The subclasses are:
 * ```
 * - AstEntity
 *   - AstSymbol
 *   - AstSyntheticEntity
 *     - AstImport
 *     - AstNamespaceImport
 * ```
 */
class AstEntity {
}
exports.AstEntity = AstEntity;
/**
 * `AstSyntheticEntity` is the abstract base class for analyzer objects whose emitted declarations
 * are not text transformations performed by the `Span` helper.
 *
 * @remarks
 * Most of API Extractor's output is produced by using the using the `Span` utility to regurgitate strings from
 * the input .d.ts files.  If we need to rename an identifier, the `Span` visitor can pick out an interesting
 * node and rewrite its string, but otherwise the transformation operates on dumb text and not compiler concepts.
 * (Historically we did this because the compiler's emitter was an internal API, but it still has some advantages,
 * for example preserving syntaxes generated by an older compiler to avoid incompatibilities.)
 *
 * This strategy does not work for cases where the output looks very different from the input.  Today these
 * cases are always kinds of `import` statements, but that may change in the future.
 */
class AstSyntheticEntity extends AstEntity {
}
exports.AstSyntheticEntity = AstSyntheticEntity;
//# sourceMappingURL=AstEntity.js.map