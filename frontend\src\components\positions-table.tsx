"use client"

import * as React from "react"
import Image from "next/image"
import { Share2, <PERSON><PERSON><PERSON>, DollarSign, ChevronUp, ChevronDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardMatch } from "@/lib/betexplorer-types"
import "@/styles/bookmaker-logos.css"

interface PositionsTableProps {
  className?: string
  // Props antigas (para compatibilidade)
  gameTitle?: string
  league?: string
  time?: string
  // Props novas (dados reais)
  matchData?: DashboardMatch
  loading?: boolean
  error?: string | null
  onRefresh?: () => void
}

export default function PositionsTable({
  className,
  // Props antigas (fallback)
  gameTitle = "Real Madrid x Inter de Milão",
  league = "Champions League",
  // Props novas
  matchData,
  loading = false,
  error = null
}: PositionsTableProps) {
  const [activeMarket, setActiveMarket] = React.useState("1X2")
  const [isCollapsed, setIsCollapsed] = React.useState(true) // Recolhido por padrão

  // Determinar dados a serem exibidos (priorizar matchData)
  const displayData = React.useMemo(() => {
    if (matchData) {
      console.log('🎯 PositionsTable recebeu matchData:', {
        homeTeam: matchData.homeTeam,
        awayTeam: matchData.awayTeam,
        score: matchData.score,
        minute: matchData.minute,
        finished: matchData.finished,
        isLive: matchData.isLive,
        hasHomeLogo: !!matchData.homeTeamLogo,
        hasAwayLogo: !!matchData.awayTeamLogo
      })

      return {
        homeTeam: matchData.homeTeam,
        awayTeam: matchData.awayTeam,
        homeTeamLogo: matchData.homeTeamLogo,
        awayTeamLogo: matchData.awayTeamLogo,
        score: matchData.score,
        minute: matchData.minute,
        finished: matchData.finished,
        competition: matchData.competition,
        country: matchData.country,
        isLive: matchData.isLive,
        odds: matchData.odds
      }
    }

    // Fallback para dados estáticos
    const teams = gameTitle.split(' x ')
    return {
      homeTeam: teams[0]?.trim() || 'Time Casa',
      awayTeam: teams[1]?.trim() || 'Time Visitante',
      homeTeamLogo: undefined,
      awayTeamLogo: undefined,
      score: '0:0',
      minute: 0,
      finished: false,
      competition: league,
      country: '',
      isLive: false,
      odds: {
        live_odds_1x2: undefined,
        live_odds_ou: undefined,
        live_odds_dc: undefined,
        live_odds_dnb: undefined,
        live_odds_btts: undefined,
        live_odds_ah: undefined
      }
    }
  }, [matchData, gameTitle, league])

  // Função para detectar se o jogo está no intervalo
  const isHalfTime = React.useMemo(() => {
    if (!displayData.competition) return false
    return displayData.competition.toLowerCase().includes('half-time')
  }, [displayData.competition])

  // Função para detectar se o jogo está em tempo extra
  const isExtraTime = React.useMemo(() => {
    if (!displayData.competition) return false
    return displayData.competition.toLowerCase().includes('extra time')
  }, [displayData.competition])

  // Função para obter o escudo do time
  const getTeamShield = (teamName: string) => {
    const lowerName = teamName.toLowerCase()
    console.log('Checking team:', teamName, 'lowercase:', lowerName) // Debug
    if (lowerName.includes('real madrid')) {
      console.log('Found Real Madrid shield') // Debug
      return '/real madrid.png'
    }
    if (lowerName.includes('inter')) {
      console.log('Found Inter shield') // Debug
      return '/inter shield.png'
    }
    // Para outros times, retornamos um placeholder ou null
    // Você pode adicionar mais escudos conforme necessário
    console.log('No shield found for:', teamName) // Debug
    return null
  }

  // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME
  const renderGameTitleWithShields = () => {
    const homeTeam = displayData.homeTeam
    const awayTeam = displayData.awayTeam
    const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam)
    const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam)

    return (
      <div className="flex items-center gap-3">
        {/* Time da casa: NOME > ESCUDO */}
        <div className="min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden">
          <span className="text-xs text-foreground">
            {homeTeam}
          </span>
          {homeShield && (
            <Image
              src={homeShield}
              alt={homeTeam}
              width={24}
              height={24}
              className="rounded-sm"
            />
          )}
        </div>

        {/* Placar e status do jogo */}
        <div className="flex flex-col items-center mx-4">
          <span className="text-xs font-semibold text-muted-foreground">
            {displayData.score || '0:0'}
          </span>
          {isHalfTime && (
            <span className="text-xs text-orange-500 font-medium">
              HT
            </span>
          )}
          {isExtraTime && (
            <span className="text-xs text-blue-500 font-medium">
              {displayData.minute}&apos;
            </span>
          )}

          {displayData.finished && (
            <span className="text-xs text-muted-foreground">
              FT
            </span>
          )}
        </div>

        {/* Time visitante: ESCUDO < NOME */}
        <div className="min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden">
          {awayShield && (
            <Image
              src={awayShield}
              alt={awayTeam}
              width={24}
              height={24}
              className="rounded-sm"
            />
          )}
          <span className="text-xs text-foreground">
            {awayTeam}
          </span>
        </div>
      </div>
    )
  }

  const markets = [
    { id: "1X2", label: "1X2" },
    { id: "OU", label: "O/U" },
    { id: "AH", label: "AH" },
    { id: "DNB", label: "DNB" },
    { id: "DC", label: "DC" },
    { id: "BTTS", label: "BTTS" },
  ]

  // Função para obter informações do logo da casa de apostas usando sprite sheet
  const getBookmakerLogoInfo = (name: string, id?: string): { useSpriteSheet: boolean; spritePosition?: string; fallbackUrl?: string } => {
    // Mapeamento das casas de apostas para suas posições na sprite sheet
    // Baseado nos IDs conhecidos das casas brasileiras
    const spriteMap: Record<string, string> = {
      '574': 'betano', // Betano.br
      '833': 'estrelabet', // Estrelabet
      '933': 'superbet', // Superbet.br
      '935': 'kto', // KTO.br
      '959': 'esportivabet', // Esportivabet
      '999': 'br4bet', // BR4Bet
      '1013': 'betmgm', // BetMGM.br
    }

    // Mapeamento por nome como fallback
    const nameMap: Record<string, string> = {
      'Betano.br': 'betano',
      'Estrelabet': 'estrelabet',
      'Superbet.br': 'superbet',
      'KTO.br': 'kto',
      'Esportivabet': 'esportivabet',
      'BR4Bet': 'br4bet',
      'BetMGM.br': 'betmgm'
    }

    // Tentar mapear por ID primeiro, depois por nome
    const spriteKey = (id && spriteMap[id]) || nameMap[name]

    if (spriteKey) {
      return {
        useSpriteSheet: true,
        spritePosition: spriteKey
      }
    }

    return {
      useSpriteSheet: false,
      fallbackUrl: undefined
    }
  }

  // Função para gerar iniciais da casa de apostas como fallback
  const getBookmakerInitials = (name: string): string => {
    return name
      .split(/[\s\.]/)
      .filter(word => word.length > 0)
      .map(word => word[0].toUpperCase())
      .slice(0, 2)
      .join('')
  }

  // Componente para renderizar logo da casa de apostas
  const BookmakerLogo = ({ name, id, size = 'lg' }: { name: string; id: string; size?: 'sm' | 'md' | 'lg' }) => {
    const logoInfo = getBookmakerLogoInfo(name, id)
    const initials = getBookmakerInitials(name)
    const iconClasses = size === 'sm' ? 'w-6 h-6' : size === 'md' ? 'w-8 h-8' : 'w-20 h-8'

    if (logoInfo.useSpriteSheet && logoInfo.spritePosition) {
      // Usar sprite sheet do BetExplorer
      return (
        <div
          className={`${iconClasses} bg-no-repeat bg-cover rounded-sm`}
          style={{
            backgroundImage: 'url(https://www.betexplorer.com/gres/images/bookmakerlogos.png?serial=2507141204BR)',
            backgroundPosition: `var(--${logoInfo.spritePosition}-position, 0 0)` // CSS custom property para posição
          }}
          title={name}
        />
      )
    }

    // Fallback para iniciais
    return (
      <div className={`${iconClasses} bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center`}>
        {initials}
      </div>
    )
  }

  // Função helper para renderizar célula da casa de apostas
  const renderBookmakerCell = (bookmaker: any, bookmakerId: string, size: 'sm' | 'md' | 'lg' = 'lg') => {
    const bookmakerName = bookmaker.name || `Casa ${bookmakerId}`
    const textSize = size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg'

    return (
      <td className="p-4">
        <div className="flex items-center gap-3">
          <BookmakerLogo name={bookmakerName} id={bookmakerId} size={size} />
          <span className={`text-foreground font-medium ${textSize}`}>
            {bookmakerName}
          </span>
        </div>
      </td>
    )
  }



  // Função para obter as odds do mercado ativo
  const getMarketOdds = (): any => {
    if (!displayData.odds) {
      console.log('⚠️ Nenhuma odd disponível')
      return null
    }

    let marketData: any

    switch (activeMarket) {
      case "1X2":
        marketData = displayData.odds.live_odds_1x2
        break
      case "OU":
        marketData = displayData.odds.live_odds_ou
        break
      case "AH":
        marketData = displayData.odds.live_odds_ah
        break
      case "DNB":
        marketData = displayData.odds.live_odds_dnb
        break
      case "DC":
        marketData = displayData.odds.live_odds_dc
        break
      case "BTTS":
        marketData = displayData.odds.live_odds_btts
        break
      default:
        marketData = null
    }

    if (!marketData) {
      console.log(`⚠️ Nenhuma odd disponível para o mercado ${activeMarket}`)
      return null
    }

    console.log(`📊 Odds encontradas para ${activeMarket}:`, marketData)
    return marketData
  }

  // Função para obter os cabeçalhos do mercado ativo
  const getMarketHeaders = (): string[] => {
    switch (activeMarket) {
      case "1X2":
        return ["Casa de Apostas", "1", "X", "2"]
      case "OU":
        return ["Casa de Apostas", "Linha", "Over", "Under"]
      case "AH":
        return ["Casa de Apostas", `${displayData.homeTeam}`, `${displayData.awayTeam}`]
      case "DNB":
        return ["Casa de Apostas", `${displayData.homeTeam}`, `${displayData.awayTeam}`]
      case "DC":
        return ["Casa de Apostas", "1X", "12", "X2"]
      case "BTTS":
        return ["Casa de Apostas", "Sim", "Não"]
      default:
        return ["Casa de Apostas"]
    }
  }

  // Obter dados das odds para o mercado ativo
  const marketOdds = getMarketOdds()
  const marketHeaders = getMarketHeaders()
  const hasOdds = marketOdds && (
    (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0) ||
    (typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0)
  )

  // Debug: Log das odds para entender a estrutura
  React.useEffect(() => {
    if (displayData.odds) {
      console.log('🎲 Odds disponíveis:', displayData.odds)
      console.log('🎯 Mercado ativo:', activeMarket)
      console.log('📊 Odds do mercado:', marketOdds)
    }
  }, [displayData.odds, activeMarket, marketOdds])

  return (
    <div className={`bg-background flex flex-col h-full ${className}`}>
      {/* Container da tabela - sempre visível mas com conteúdo condicional */}
      <div className={`${isCollapsed ? 'flex-shrink-0' : 'flex-1'} m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar`}>

        {/* Header com grid de 3 colunas para estado e jogos + botões separados */}
        <div className="px-6 py-1 border-border">
          <div className="flex items-center justify-between min-h-[32px]">

            {/* Grid de 3 colunas: [STATUS] [GAME] [STATUS] */}
            <div className="flex  items-center flex-1">

              {/* Coluna 1: Status do Jogo */}
              <div className="flex justify-start basis-[10%]">
                {(displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && (
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                      displayData.finished
                        ? 'bg-gray-500'
                        : isExtraTime
                          ? 'bg-blue-500 animate-pulse'
                          : isHalfTime
                            ? 'bg-orange-500 animate-pulse'
                            : 'bg-green-500 animate-pulse'
                    }`}></div>
                    <span className={`text-xs font-medium whitespace-nowrap ${
                      displayData.finished
                        ? 'text-gray-500'
                        : isExtraTime
                          ? 'text-blue-500'
                          : isHalfTime
                            ? 'text-orange-500'
                            : 'text-green-500'
                    }`}>
                      {displayData.finished
                        ? 'FINALIZADO'
                        : isExtraTime
                          ? 'TEMPO EXTRA'
                          : isHalfTime
                            ? 'INTERVALO'
                            : `${displayData.minute ? displayData.minute + "'" : ''}`}
                    </span>
                  </div>
                )}
              </div>

              {/* Coluna 2: Container do Jogo (Centralizado) */}
            
              <div className="max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center">
                {renderGameTitleWithShields()}
                <p className="text-sm text-muted-foreground mt-1">
                  {(() => {
                    const filterGameInfo = (text: string) => {
                      if (!text) return false;
                      const lowerText = text.toLowerCase();
                      return !lowerText.includes('half') &&
                             !lowerText.includes('tempo') &&
                             !lowerText.includes('1st') &&
                             !lowerText.includes('2nd') &&
                             !lowerText.includes('1º') &&
                             !lowerText.includes('2º') &&
                             !lowerText.includes('primeiro') &&
                             !lowerText.includes('segundo') &&
                             !lowerText.includes('finished') &&
                             !lowerText.includes('extra time') &&
                             !lowerText.includes('postponed');
                    };

                    const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';
                    const country = filterGameInfo(displayData.country) ? displayData.country : '';

                    return (
                      <>
                        {competition}
                        {competition && country && ' • '}
                        {country}
                      </>
                    );
                  })()}
                  {loading && ' • Carregando...'}
                  {error && ' • Erro ao carregar'}
                </p>
              </div>

              {/* Coluna 3: Vazia (para equilibrar o grid) */}
              <div></div>
            </div>

            {/* Botões de Ação (fora do grid) */}
            <div className="flex items-center gap-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de compartilhar */}}
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de configurações */}}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => {/* Ação de dólar */}}
              >
                <DollarSign className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Conteúdo da tabela - só visível quando expandido */}
        {!isCollapsed && (
          <>
            {/* Toggles de Mercados */}
            <div className="px-6 py-4 border-b border-border">
              <div className="flex items-center gap-2">
                {markets.map((market) => (
                  <button
                    key={market.id}
                    onClick={() => setActiveMarket(market.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeMarket === market.id
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground"
                    }`}
                  >
                    {market.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tabela */}
            <table className="w-full text-sm">
              <thead className="backdrop-blur-sm">
                <tr>
                  {marketHeaders.map((header, index) => (
                    <th key={index} className="text-left p-4 font-medium text-muted-foreground">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {hasOdds ? (
                  (() => {
                    // Renderizar baseado na estrutura do mercado
                    if (activeMarket === "1X2" && marketOdds?.bookmakers) {
                      return Object.entries(marketOdds.bookmakers).map(([bookmakerId, bookmaker]: [string, any]) => (
                        <tr key={bookmakerId} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                          {renderBookmakerCell(bookmaker, bookmakerId)}
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["1"]?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker.x?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["2"]?.current || "-"}
                            </span>
                          </td>
                        </tr>
                      ))
                    }

                    if (activeMarket === "OU" && marketOdds) {
                      // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)
                      const lines = Object.keys(marketOdds).filter(key => key !== 'bookmakers' && key !== 'average' && key !== 'highest')
                      return lines.flatMap(line => {
                        const lineData = marketOdds[line]
                        if (!lineData?.bookmakers) return []

                        return Object.entries(lineData.bookmakers).map(([bookmakerId, bookmaker]: [string, any]) => (
                          <tr key={`${line}-${bookmakerId}`} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                            {renderBookmakerCell(bookmaker, bookmakerId, 'md')}
                            <td className="p-4 text-muted-foreground font-medium">
                              {line}
                            </td>
                            <td className="p-4">
                              <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                                {bookmaker.over?.current || "-"}
                              </span>
                            </td>
                            <td className="p-4">
                              <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                                {bookmaker.under?.current || "-"}
                              </span>
                            </td>
                          </tr>
                        ))
                      })
                    }

                    if (activeMarket === "DC" && marketOdds?.bookmakers) {
                      return Object.entries(marketOdds.bookmakers).map(([bookmakerId, bookmaker]: [string, any]) => (
                        <tr key={bookmakerId} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                          {renderBookmakerCell(bookmaker, bookmakerId)}
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["1x"]?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["12"]?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker.x2?.current || "-"}
                            </span>
                          </td>
                        </tr>
                      ))
                    }

                    if (activeMarket === "DNB" && marketOdds?.bookmakers) {
                      return Object.entries(marketOdds.bookmakers).map(([bookmakerId, bookmaker]: [string, any]) => (
                        <tr key={bookmakerId} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                          {renderBookmakerCell(bookmaker, bookmakerId)}
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["1"]?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker["2"]?.current || "-"}
                            </span>
                          </td>
                        </tr>
                      ))
                    }

                    if (activeMarket === "BTTS" && marketOdds?.bookmakers) {
                      return Object.entries(marketOdds.bookmakers).map(([bookmakerId, bookmaker]: [string, any]) => (
                        <tr key={bookmakerId} className="border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200">
                          {renderBookmakerCell(bookmaker, bookmakerId)}
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker.yes?.current || "-"}
                            </span>
                          </td>
                          <td className="p-4">
                            <span className="inline-flex items-center justify-center px-3 py-2 rounded bg-primary/10 text-primary font-medium min-w-[60px]">
                              {bookmaker.no?.current || "-"}
                            </span>
                          </td>
                        </tr>
                      ))
                    }

                    // Fallback para outros mercados
                    return (
                      <tr>
                        <td colSpan={marketHeaders.length} className="p-6 text-center text-muted-foreground">
                          Estrutura de dados não suportada para este mercado
                        </td>
                      </tr>
                    )
                  })()
                ) : (
                  <tr>
                    <td colSpan={marketHeaders.length} className="p-6 text-center text-muted-foreground">
                      {loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </>
        )}
      </div>

    
    </div>
  )
}
