"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para obter o logo da casa de apostas\n    const getBookmakerLogo = (bookmakerName)=>{\n        const name = bookmakerName.toLowerCase();\n        // Mapeamento dos logos das casas de apostas\n        const logoMap = {\n            'betano.br': '/bookmakers/betano.png',\n            'betano': '/bookmakers/betano.png',\n            'estrelabet': '/bookmakers/estrelabet.png',\n            'superbet.br': '/bookmakers/superbet.png',\n            'superbet': '/bookmakers/superbet.png',\n            'kto.br': '/bookmakers/kto.png',\n            'kto': '/bookmakers/kto.png',\n            'esportivabet': '/bookmakers/esportivabet.png',\n            'br4bet': '/bookmakers/br4bet.png',\n            'betmgm.br': '/bookmakers/betmgm.png',\n            'betmgm': '/bookmakers/betmgm.png'\n        };\n        // Procurar por correspondência parcial\n        for (const [key, logo] of Object.entries(logoMap)){\n            if (name.includes(key)) {\n                return logo;\n            }\n        }\n        return null;\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Linha\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = marketOdds && (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0 || typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0);\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? (()=>{\n                                        // Renderizar baseado na estrutura do mercado\n                                        if (activeMarket === \"1X2\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_x, _bookmaker_2;\n                                                const logo = getBookmakerLogo(bookmaker.name || '');\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: logo,\n                                                                        alt: bookmaker.name || \"Casa \".concat(bookmakerId),\n                                                                        width: 24,\n                                                                        height: 24,\n                                                                        className: \"rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 35\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x = bookmaker.x) === null || _bookmaker_x === void 0 ? void 0 : _bookmaker_x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 27\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"OU\" && marketOdds) {\n                                            // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)\n                                            const lines = Object.keys(marketOdds).filter((key)=>key !== 'bookmakers' && key !== 'average' && key !== 'highest');\n                                            return lines.flatMap((line)=>{\n                                                const lineData = marketOdds[line];\n                                                if (!(lineData === null || lineData === void 0 ? void 0 : lineData.bookmakers)) return [];\n                                                return Object.entries(lineData.bookmakers).map((param)=>{\n                                                    let [bookmakerId, bookmaker] = param;\n                                                    var _bookmaker_over, _bookmaker_under;\n                                                    const logo = getBookmakerLogo(bookmaker.name || '');\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-foreground font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: logo,\n                                                                            alt: bookmaker.name || \"Casa \".concat(bookmakerId),\n                                                                            width: 24,\n                                                                            height: 24,\n                                                                            className: \"rounded-sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-muted-foreground font-medium\",\n                                                                children: line\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_over = bookmaker.over) === null || _bookmaker_over === void 0 ? void 0 : _bookmaker_over.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_under = bookmaker.under) === null || _bookmaker_under === void 0 ? void 0 : _bookmaker_under.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(line, \"-\").concat(bookmakerId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 29\n                                                    }, this);\n                                                });\n                                            });\n                                        }\n                                        if (activeMarket === \"DC\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1x, _bookmaker_12, _bookmaker_x2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1x = bookmaker[\"1x\"]) === null || _bookmaker_1x === void 0 ? void 0 : _bookmaker_1x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_12 = bookmaker[\"12\"]) === null || _bookmaker_12 === void 0 ? void 0 : _bookmaker_12.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x2 = bookmaker.x2) === null || _bookmaker_x2 === void 0 ? void 0 : _bookmaker_x2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"DNB\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"BTTS\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_yes, _bookmaker_no;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3 text-foreground font-medium\",\n                                                            children: bookmaker.name || \"Casa \".concat(bookmakerId)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_yes = bookmaker.yes) === null || _bookmaker_yes === void 0 ? void 0 : _bookmaker_yes.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_no = bookmaker.no) === null || _bookmaker_no === void 0 ? void 0 : _bookmaker_no.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        // Fallback para outros mercados\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: marketHeaders.length,\n                                                className: \"p-6 text-center text-muted-foreground\",\n                                                children: \"Estrutura de dados n\\xe3o suportada para este mercado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 23\n                                        }, this);\n                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});