/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/bookmaker-logos.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/* 
 * Posições dos logos das casas de apostas na sprite sheet do BetExplorer
 * URL: https://www.betexplorer.com/gres/images/bookmakerlogos.png?serial=2507141204BR
 * 
 * Nota: As posições precisam ser ajustadas baseado na análise da sprite sheet real
 * Estas são posições estimadas que precisam ser refinadas
 */

:root {
  /* Logos das principais casas de apostas brasileiras */
  --betano-position: 0 0;
  --estrelabet-position: -24px 0;
  --superbet-position: -48px 0;
  --kto-position: -72px 0;
  --esportivabet-position: -96px 0;
  --br4bet-position: -120px 0;
  --betmgm-position: -144px 0;
  
  /* Logos de casas internacionais (se disponíveis) */
  --bet365-position: -168px 0;
  --pinnacle-position: -192px 0;
  --1xbet-position: -216px 0;
}

/*
 * Classes auxiliares para diferentes tamanhos de logo
 */
.bookmaker-logo-sm {
  width: 24px;
  height: 24px;
  background-size: auto 24px;
}

.bookmaker-logo-md {
  width: 32px;
  height: 32px;
  background-size: auto 32px;
}

.bookmaker-logo-lg {
  width: 40px;
  height: 40px;
  background-size: auto 40px;
}

/* 
 * Fallback para iniciais quando logo não está disponível
 */
.bookmaker-initials {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.75rem;
  border-radius: 0.125rem;
  background-color: rgba(var(--primary), 0.1);
  color: rgb(var(--primary));
}

/* 
 * Animação suave para carregamento de logos
 */
.bookmaker-logo {
  transition: opacity 0.2s ease-in-out;
}

.bookmaker-logo:not([style*="background-image"]) {
  opacity: 0;
}

.bookmaker-logo[style*="background-image"] {
  opacity: 1;
}

/* 
 * Estados de hover para melhor UX
 */
.bookmaker-cell:hover .bookmaker-logo,
.bookmaker-cell:hover .bookmaker-initials {
  transform: scale(1.05);
  transition: transform 0.15s ease-in-out;
}

/*
 * Responsividade para telas menores
 */
@media (max-width: 768px) {
  .bookmaker-logo-sm {
    width: 20px;
    height: 20px;
    background-size: auto 20px;
  }

  .bookmaker-logo-md {
    width: 28px;
    height: 28px;
    background-size: auto 28px;
  }

  .bookmaker-logo-lg {
    width: 32px;
    height: 32px;
    background-size: auto 32px;
  }
}

/* 
 * Modo escuro - ajustes para melhor contraste
 */
@media (prefers-color-scheme: dark) {
  .bookmaker-initials {
    background-color: rgba(var(--primary), 0.2);
  }
}

/* 
 * Placeholder para logos não carregados
 */
.bookmaker-logo-placeholder {
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 4px 4px;
  background-position: 0 0, 0 2px, 2px -2px, -2px 0px;
  opacity: 0.3;
}

/* 
 * Indicador de carregamento
 */
.bookmaker-logo-loading {
  position: relative;
  overflow: hidden;
}

.bookmaker-logo-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

