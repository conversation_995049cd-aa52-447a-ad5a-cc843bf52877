{"version": 3, "file": "ExtractorMessage.js", "sourceRoot": "", "sources": ["../../src/api/ExtractorMessage.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D,2DAAwD;AAExD,yFAAsF;AAmBtF;;;GAGG;AACH,IAAY,wBAqCX;AArCD,WAAY,wBAAwB;IAClC;;;;;;OAMG;IACH,iDAAqB,CAAA;IAErB;;;;;;OAMG;IACH,2CAAe,CAAA;IAEf;;;;;;OAMG;IACH,mDAAuB,CAAA;IAEvB;;;;;;;OAOG;IACH,+CAAmB,CAAA;AACrB,CAAC,EArCW,wBAAwB,wCAAxB,wBAAwB,QAqCnC;AAgBD;;;;GAIG;AACH,MAAa,gBAAgB;IA2C3B,gBAAgB;IAChB,YAAmB,OAAiC;QAClD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAE3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,IAAI,qCAAiB,CAAC,IAAI,CAAC;IAC9D,CAAC;IAED;;;;;;;;;;;OAWG;IACH,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAW,OAAO,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAW,QAAQ,CAAC,KAAwB;QAC1C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,qCAAiB,CAAC,KAAK,CAAC;YAC7B,KAAK,qCAAiB,CAAC,IAAI,CAAC;YAC5B,KAAK,qCAAiB,CAAC,IAAI,CAAC;YAC5B,KAAK,qCAAiB,CAAC,OAAO,CAAC;YAC/B,KAAK,qCAAiB,CAAC,OAAO;gBAC5B,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;;;;;;OAOG;IACI,yBAAyB,CAAC,wBAA4C;QAC3E,IAAI,MAAM,GAAW,EAAE,CAAC;QAExB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,IAAI,yDAA2B,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE;gBACpE,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACvC,wBAAwB;aACzB,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC;YAClB,CAAC;QACH,CAAC;QAED,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAE9C,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,4BAA4B;QACjC,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;CACF;AAhJD,4CAgJC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport type * as tsdoc from '@microsoft/tsdoc';\nimport type { ExtractorMessageId } from './ExtractorMessageId';\nimport { ExtractorLogLevel } from './ExtractorLogLevel';\nimport type { ConsoleMessageId } from './ConsoleMessageId';\nimport { SourceFileLocationFormatter } from '../analyzer/SourceFileLocationFormatter';\n\n/**\n * Used by {@link ExtractorMessage.properties}.\n *\n * @public\n */\nexport interface IExtractorMessageProperties {\n  /**\n   * A declaration can have multiple names if it is exported more than once.\n   * If an `ExtractorMessage` applies to a specific export name, this property can indicate that.\n   *\n   * @remarks\n   *\n   * Used by {@link ExtractorMessageId.InternalMissingUnderscore}.\n   */\n  readonly exportName?: string;\n}\n\n/**\n * Specifies a category of messages for use with {@link ExtractorMessage}.\n * @public\n */\nexport enum ExtractorMessageCategory {\n  /**\n   * Messages originating from the TypeScript compiler.\n   *\n   * @remarks\n   * These strings begin with the prefix \"TS\" and have a numeric error code.\n   * Example: `TS2551`\n   */\n  Compiler = 'Compiler',\n\n  /**\n   * Messages related to parsing of TSDoc comments.\n   *\n   * @remarks\n   * These strings begin with the prefix \"tsdoc-\".\n   * Example: `tsdoc-link-tag-unescaped-text`\n   */\n  TSDoc = 'TSDoc',\n\n  /**\n   * Messages related to API Extractor's analysis.\n   *\n   * @remarks\n   * These strings begin with the prefix \"ae-\".\n   * Example: `ae-extra-release-tag`\n   */\n  Extractor = 'Extractor',\n\n  /**\n   * Console messages communicate the progress of the overall operation.  They may include newlines to ensure\n   * nice formatting.  They are output in real time, and cannot be routed to the API Report file.\n   *\n   * @remarks\n   * These strings begin with the prefix \"console-\".\n   * Example: `console-writing-typings-file`\n   */\n  Console = 'console'\n}\n\n/**\n * Constructor options for `ExtractorMessage`.\n */\nexport interface IExtractorMessageOptions {\n  category: ExtractorMessageCategory;\n  messageId: tsdoc.TSDocMessageId | ExtractorMessageId | ConsoleMessageId | string;\n  text: string;\n  sourceFilePath?: string;\n  sourceFileLine?: number;\n  sourceFileColumn?: number;\n  properties?: IExtractorMessageProperties;\n  logLevel?: ExtractorLogLevel;\n}\n\n/**\n * This object is used to report an error or warning that occurred during API Extractor's analysis.\n *\n * @public\n */\nexport class ExtractorMessage {\n  private _handled: boolean;\n  private _logLevel: ExtractorLogLevel;\n\n  /**\n   * The category of issue.\n   */\n  public readonly category: ExtractorMessageCategory;\n\n  /**\n   * A text string that uniquely identifies the issue type.  This identifier can be used to suppress\n   * or configure the reporting of issues, and also to search for help about an issue.\n   */\n  public readonly messageId: tsdoc.TSDocMessageId | ExtractorMessageId | ConsoleMessageId | string;\n\n  /**\n   * The text description of this issue.\n   */\n  public readonly text: string;\n\n  /**\n   * The absolute path to the affected input source file, if there is one.\n   */\n  public readonly sourceFilePath: string | undefined;\n\n  /**\n   * The line number where the issue occurred in the input source file.  This is not used if `sourceFilePath`\n   * is undefined.  The first line number is 1.\n   */\n  public readonly sourceFileLine: number | undefined;\n\n  /**\n   * The column number where the issue occurred in the input source file.  This is not used if `sourceFilePath`\n   * is undefined.  The first column number is 1.\n   */\n  public readonly sourceFileColumn: number | undefined;\n\n  /**\n   * Additional contextual information about the message that may be useful when reporting errors.\n   * All properties are optional.\n   */\n  public readonly properties: IExtractorMessageProperties;\n\n  /** @internal */\n  public constructor(options: IExtractorMessageOptions) {\n    this.category = options.category;\n    this.messageId = options.messageId;\n    this.text = options.text;\n    this.sourceFilePath = options.sourceFilePath;\n    this.sourceFileLine = options.sourceFileLine;\n    this.sourceFileColumn = options.sourceFileColumn;\n    this.properties = options.properties || {};\n\n    this._handled = false;\n    this._logLevel = options.logLevel || ExtractorLogLevel.None;\n  }\n\n  /**\n   * If the {@link IExtractorInvokeOptions.messageCallback} sets this property to true, it will prevent the message\n   * from being displayed by API Extractor.\n   *\n   * @remarks\n   * If the `messageCallback` routes the message to a custom handler (e.g. a toolchain logger), it should\n   * assign `handled = true` to prevent API Extractor from displaying it.  Assigning `handled = true` for all messages\n   * would effectively disable all console output from the `Extractor` API.\n   *\n   * If `handled` is set to true, the message will still be included in the count of errors/warnings;\n   * to discard a message entirely, instead assign `logLevel = none`.\n   */\n  public get handled(): boolean {\n    return this._handled;\n  }\n\n  public set handled(value: boolean) {\n    if (this._handled && !value) {\n      throw new Error(\n        'One a message has been marked as handled, the \"handled\" property cannot be set to false'\n      );\n    }\n    this._handled = value;\n  }\n\n  /**\n   * Specifies how the message should be reported.\n   *\n   * @remarks\n   * If the {@link IExtractorInvokeOptions.messageCallback} handles the message (i.e. sets `handled = true`),\n   * it can use the `logLevel` to determine how to display the message.\n   *\n   * Alternatively, if API Extractor is handling the message, the `messageCallback` could assign `logLevel` to change\n   * how it will be processed.  However, in general the recommended practice is to configure message routing\n   * using the `messages` section in api-extractor.json.\n   *\n   * To discard a message entirely, assign `logLevel = none`.\n   */\n  public get logLevel(): ExtractorLogLevel {\n    return this._logLevel;\n  }\n\n  public set logLevel(value: ExtractorLogLevel) {\n    switch (value) {\n      case ExtractorLogLevel.Error:\n      case ExtractorLogLevel.Info:\n      case ExtractorLogLevel.None:\n      case ExtractorLogLevel.Verbose:\n      case ExtractorLogLevel.Warning:\n        break;\n      default:\n        throw new Error('Invalid log level');\n    }\n    this._logLevel = value;\n  }\n\n  /**\n   * Returns the message formatted with its identifier and file position.\n   * @remarks\n   * Example:\n   * ```\n   * src/folder/File.ts:123:4 - (ae-extra-release-tag) The doc comment should not contain more than one release tag.\n   * ```\n   */\n  public formatMessageWithLocation(workingPackageFolderPath: string | undefined): string {\n    let result: string = '';\n\n    if (this.sourceFilePath) {\n      result += SourceFileLocationFormatter.formatPath(this.sourceFilePath, {\n        sourceFileLine: this.sourceFileLine,\n        sourceFileColumn: this.sourceFileColumn,\n        workingPackageFolderPath\n      });\n\n      if (result.length > 0) {\n        result += ' - ';\n      }\n    }\n\n    result += this.formatMessageWithoutLocation();\n\n    return result;\n  }\n\n  public formatMessageWithoutLocation(): string {\n    return `(${this.messageId}) ${this.text}`;\n  }\n}\n"]}