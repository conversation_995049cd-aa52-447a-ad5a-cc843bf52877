{"version": 3, "file": "AstModule.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstModule.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AA+B3D;;GAEG;AACH,MAAa,SAAS;IAkCpB,YAAmB,OAA0B;QAC3C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAErD,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAa,CAAC;QAEhD,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAqB,CAAC;QAE3D,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAAS,CAAC;IAC/C,CAAC;CACF;AArDD,8BAqDC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type * as ts from 'typescript';\n\nimport type { AstSymbol } from './AstSymbol';\nimport type { AstEntity } from './AstEntity';\n\n/**\n * Represents information collected by {@link AstSymbolTable.fetchAstModuleExportInfo}\n */\nexport interface IAstModuleExportInfo {\n  readonly visitedAstModules: Set<AstModule>;\n  readonly exportedLocalEntities: Map<string, AstEntity>;\n  readonly starExportedExternalModules: Set<AstModule>;\n}\n\n/**\n * Constructor parameters for AstModule\n *\n * @privateRemarks\n * Our naming convention is to use I____Parameters for constructor options and\n * I____Options for general function options.  However the word \"parameters\" is\n * confusingly similar to the terminology for function parameters modeled by API Extractor,\n * so we use I____Options for both cases in this code base.\n */\nexport interface IAstModuleOptions {\n  sourceFile: ts.SourceFile;\n  moduleSymbol: ts.Symbol;\n  externalModulePath: string | undefined;\n}\n\n/**\n * An internal data structure that represents a source file that is analyzed by AstSymbolTable.\n */\nexport class AstModule {\n  /**\n   * The source file that declares this TypeScript module.  In most cases, the source file's\n   * top-level exports constitute the module.\n   */\n  public readonly sourceFile: ts.SourceFile;\n\n  /**\n   * The symbol for the module.  Typically this corresponds to ts.SourceFile itself, however\n   * in some cases the ts.SourceFile may contain multiple modules declared using the `module` keyword.\n   */\n  public readonly moduleSymbol: ts.Symbol;\n\n  /**\n   * Example:  \"@rushstack/node-core-library/lib/FileSystem\"\n   * but never: \"./FileSystem\"\n   */\n  public readonly externalModulePath: string | undefined;\n\n  /**\n   * A list of other `AstModule` objects that appear in `export * from \"___\";` statements.\n   */\n  public readonly starExportedModules: Set<AstModule>;\n\n  /**\n   * A partial map of entities exported by this module.  The key is the exported name.\n   */\n  public readonly cachedExportedEntities: Map<string, AstEntity>; // exportName --> entity\n\n  /**\n   * Additional state calculated by `AstSymbolTable.fetchWorkingPackageModule()`.\n   */\n  public astModuleExportInfo: IAstModuleExportInfo | undefined;\n\n  public constructor(options: IAstModuleOptions) {\n    this.sourceFile = options.sourceFile;\n    this.moduleSymbol = options.moduleSymbol;\n\n    this.externalModulePath = options.externalModulePath;\n\n    this.starExportedModules = new Set<AstModule>();\n\n    this.cachedExportedEntities = new Map<string, AstSymbol>();\n\n    this.astModuleExportInfo = undefined;\n  }\n\n  /**\n   * If false, then this source file is part of the working package being processed by the `Collector`.\n   */\n  public get isExternal(): boolean {\n    return this.externalModulePath !== undefined;\n  }\n}\n"]}