/**
 * This file defines the TSDoc custom tags for use with API Extractor.
 *
 * If your project has a custom tsdoc.json file, then it should use the "extends" field to
 * inherit the definitions from this file.  For example:
 *
 * ```
 * {
 *   "$schema": "https://developer.microsoft.com/json-schemas/tsdoc/v0/tsdoc.schema.json",
 *   "extends": [ "@microsoft/api-extractor/extends/tsdoc-config.json" ],
 *   . . .
 * }
 * ```
 *
 * For details about this config file, please see: https://tsdoc.org/pages/packages/tsdoc-config/
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/tsdoc/v0/tsdoc.schema.json",

  /**
   * The "AEDoc" custom tags:
   */
  "tagDefinitions": [
    {
      "tagName": "@betaDocumentation",
      "syntaxKind": "modifier"
    },
    {
      "tagName": "@internalRemarks",
      "syntaxKind": "block"
    },
    {
      "tagName": "@preapproved",
      "syntaxKind": "modifier"
    }
  ],

  /**
   * TSDoc tags implemented by API Extractor:
   */
  "supportForTags": {
    "@alpha": true,
    "@beta": true,
    "@defaultValue": true,
    "@decorator": true,
    "@deprecated": true,
    "@eventProperty": true,
    "@example": true,
    "@experimental": true,
    "@inheritDoc": true,
    "@internal": true,
    "@label": true,
    "@link": true,
    "@override": true,
    "@packageDocumentation": true,
    "@param": true,
    "@privateRemarks": true,
    "@public": true,
    "@readonly": true,
    "@remarks": true,
    "@returns": true,
    "@sealed": true,
    "@see": true,
    "@throws": true,
    "@typeParam": true,
    "@virtual": true,

    "@betaDocumentation": true,
    "@internalRemarks": true,
    "@preapproved": true
  }
}
