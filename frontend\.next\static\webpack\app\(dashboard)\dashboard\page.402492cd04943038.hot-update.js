"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/positions-table.tsx":
/*!********************************************!*\
  !*** ./src/components/positions-table.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PositionsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,DollarSign,Settings,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PositionsTable(param) {\n    let { className, // Props antigas (fallback)\n    gameTitle = \"Real Madrid x Inter de Milão\", league = \"Champions League\", // Props novas\n    matchData, loading = false, error = null } = param;\n    var _this = this;\n    _s();\n    const [activeMarket, setActiveMarket] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"1X2\");\n    const [isCollapsed, setIsCollapsed] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true) // Recolhido por padrão\n    ;\n    // Determinar dados a serem exibidos (priorizar matchData)\n    const displayData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[displayData]\": ()=>{\n            var _teams_, _teams_1;\n            if (matchData) {\n                console.log('🎯 PositionsTable recebeu matchData:', {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    isLive: matchData.isLive,\n                    hasHomeLogo: !!matchData.homeTeamLogo,\n                    hasAwayLogo: !!matchData.awayTeamLogo\n                });\n                return {\n                    homeTeam: matchData.homeTeam,\n                    awayTeam: matchData.awayTeam,\n                    homeTeamLogo: matchData.homeTeamLogo,\n                    awayTeamLogo: matchData.awayTeamLogo,\n                    score: matchData.score,\n                    minute: matchData.minute,\n                    finished: matchData.finished,\n                    competition: matchData.competition,\n                    country: matchData.country,\n                    isLive: matchData.isLive,\n                    odds: matchData.odds\n                };\n            }\n            // Fallback para dados estáticos\n            const teams = gameTitle.split(' x ');\n            return {\n                homeTeam: ((_teams_ = teams[0]) === null || _teams_ === void 0 ? void 0 : _teams_.trim()) || 'Time Casa',\n                awayTeam: ((_teams_1 = teams[1]) === null || _teams_1 === void 0 ? void 0 : _teams_1.trim()) || 'Time Visitante',\n                homeTeamLogo: undefined,\n                awayTeamLogo: undefined,\n                score: '0:0',\n                minute: 0,\n                finished: false,\n                competition: league,\n                country: '',\n                isLive: false,\n                odds: {\n                    live_odds_1x2: undefined,\n                    live_odds_ou: undefined,\n                    live_odds_dc: undefined,\n                    live_odds_dnb: undefined,\n                    live_odds_btts: undefined,\n                    live_odds_ah: undefined\n                }\n            };\n        }\n    }[\"PositionsTable.useMemo[displayData]\"], [\n        matchData,\n        gameTitle,\n        league\n    ]);\n    // Função para detectar se o jogo está no intervalo\n    const isHalfTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isHalfTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('half-time');\n        }\n    }[\"PositionsTable.useMemo[isHalfTime]\"], [\n        displayData.competition\n    ]);\n    // Função para detectar se o jogo está em tempo extra\n    const isExtraTime = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"PositionsTable.useMemo[isExtraTime]\": ()=>{\n            if (!displayData.competition) return false;\n            return displayData.competition.toLowerCase().includes('extra time');\n        }\n    }[\"PositionsTable.useMemo[isExtraTime]\"], [\n        displayData.competition\n    ]);\n    // Função para obter o escudo do time\n    const getTeamShield = (teamName)=>{\n        const lowerName = teamName.toLowerCase();\n        console.log('Checking team:', teamName, 'lowercase:', lowerName); // Debug\n        if (lowerName.includes('real madrid')) {\n            console.log('Found Real Madrid shield'); // Debug\n            return '/real madrid.png';\n        }\n        if (lowerName.includes('inter')) {\n            console.log('Found Inter shield'); // Debug\n            return '/inter shield.png';\n        }\n        // Para outros times, retornamos um placeholder ou null\n        // Você pode adicionar mais escudos conforme necessário\n        console.log('No shield found for:', teamName); // Debug\n        return null;\n    };\n    // Função para renderizar o título com escudos na ordem: NOME > ESCUDO > PLACAR < ESCUDO < NOME\n    const renderGameTitleWithShields = ()=>{\n        const homeTeam = displayData.homeTeam;\n        const awayTeam = displayData.awayTeam;\n        const homeShield = displayData.homeTeamLogo || getTeamShield(homeTeam);\n        const awayShield = displayData.awayTeamLogo || getTeamShield(awayTeam);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-end gap-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: homeTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        homeShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: homeShield,\n                            alt: homeTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-semibold text-muted-foreground\",\n                            children: displayData.score || '0:0'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        isHalfTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-orange-500 font-medium\",\n                            children: \"HT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        isExtraTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-500 font-medium\",\n                            children: [\n                                displayData.minute,\n                                \"'\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        displayData.finished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"FT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-mt:!justify-end flex min-w-0 basis-[50%] cursor-pointer items-center justify-start gap-1 overflow-hidden\",\n                    children: [\n                        awayShield && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: awayShield,\n                            alt: awayTeam,\n                            width: 24,\n                            height: 24,\n                            className: \"rounded-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-foreground\",\n                            children: awayTeam\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const markets = [\n        {\n            id: \"1X2\",\n            label: \"1X2\"\n        },\n        {\n            id: \"OU\",\n            label: \"O/U\"\n        },\n        {\n            id: \"AH\",\n            label: \"AH\"\n        },\n        {\n            id: \"DNB\",\n            label: \"DNB\"\n        },\n        {\n            id: \"DC\",\n            label: \"DC\"\n        },\n        {\n            id: \"BTTS\",\n            label: \"BTTS\"\n        }\n    ];\n    // Função para obter logo da casa de apostas\n    const getBookmakerLogo = (name)=>{\n        const logoMap = {\n            'Betano.br': '/logos/betano.png',\n            'Estrelabet': '/logos/estrelabet.png',\n            'Superbet.br': '/logos/superbet.png',\n            'KTO.br': '/logos/kto.png',\n            'Esportivabet': '/logos/esportivabet.png',\n            'BR4Bet': '/logos/br4bet.png',\n            'BetMGM.br': '/logos/betmgm.png',\n            'bet365': '/logos/bet365.png',\n            'Pinnacle': '/logos/pinnacle.png',\n            '1xBet': '/logos/1xbet.png'\n        };\n        return logoMap[name] || null;\n    };\n    // Função para gerar iniciais da casa de apostas como fallback\n    const getBookmakerInitials = (name)=>{\n        return name.split(/[\\s\\.]/).filter((word)=>word.length > 0).map((word)=>word[0].toUpperCase()).slice(0, 2).join('');\n    };\n    // Função helper para renderizar célula da casa de apostas\n    const renderBookmakerCell = function(bookmaker, bookmakerId) {\n        let size = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'md';\n        const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n        const logoUrl = getBookmakerLogo(bookmakerName);\n        const initials = getBookmakerInitials(bookmakerName);\n        const iconSize = size === 'sm' ? 20 : 24;\n        const iconClasses = size === 'sm' ? 'w-5 h-5' : 'w-6 h-6';\n        const textSize = size === 'sm' ? 'text-sm' : '';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n            className: \"p-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: logoUrl,\n                        alt: bookmakerName,\n                        width: iconSize,\n                        height: iconSize,\n                        className: \"rounded-sm\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.style.display = 'none';\n                            const parent = target.parentElement;\n                            if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                const initialsDiv = document.createElement('div');\n                                initialsDiv.className = \"bookmaker-initials \".concat(iconClasses, \" bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\");\n                                initialsDiv.textContent = initials;\n                                parent.appendChild(initialsDiv);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(iconClasses, \" bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\"),\n                        children: initials\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-foreground font-medium \".concat(textSize),\n                        children: bookmakerName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, _this);\n    };\n    // Função para obter as odds do mercado ativo\n    const getMarketOdds = ()=>{\n        if (!displayData.odds) {\n            console.log('⚠️ Nenhuma odd disponível');\n            return null;\n        }\n        let marketData;\n        switch(activeMarket){\n            case \"1X2\":\n                marketData = displayData.odds.live_odds_1x2;\n                break;\n            case \"OU\":\n                marketData = displayData.odds.live_odds_ou;\n                break;\n            case \"AH\":\n                marketData = displayData.odds.live_odds_ah;\n                break;\n            case \"DNB\":\n                marketData = displayData.odds.live_odds_dnb;\n                break;\n            case \"DC\":\n                marketData = displayData.odds.live_odds_dc;\n                break;\n            case \"BTTS\":\n                marketData = displayData.odds.live_odds_btts;\n                break;\n            default:\n                marketData = null;\n        }\n        if (!marketData) {\n            console.log(\"⚠️ Nenhuma odd dispon\\xedvel para o mercado \".concat(activeMarket));\n            return null;\n        }\n        console.log(\"\\uD83D\\uDCCA Odds encontradas para \".concat(activeMarket, \":\"), marketData);\n        return marketData;\n    };\n    // Função para obter os cabeçalhos do mercado ativo\n    const getMarketHeaders = ()=>{\n        switch(activeMarket){\n            case \"1X2\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1\",\n                    \"X\",\n                    \"2\"\n                ];\n            case \"OU\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Linha\",\n                    \"Over\",\n                    \"Under\"\n                ];\n            case \"AH\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DNB\":\n                return [\n                    \"Casa de Apostas\",\n                    \"\".concat(displayData.homeTeam),\n                    \"\".concat(displayData.awayTeam)\n                ];\n            case \"DC\":\n                return [\n                    \"Casa de Apostas\",\n                    \"1X\",\n                    \"12\",\n                    \"X2\"\n                ];\n            case \"BTTS\":\n                return [\n                    \"Casa de Apostas\",\n                    \"Sim\",\n                    \"Não\"\n                ];\n            default:\n                return [\n                    \"Casa de Apostas\"\n                ];\n        }\n    };\n    // Obter dados das odds para o mercado ativo\n    const marketOdds = getMarketOdds();\n    const marketHeaders = getMarketHeaders();\n    const hasOdds = marketOdds && (marketOdds.bookmakers && Object.keys(marketOdds.bookmakers).length > 0 || typeof marketOdds === 'object' && Object.keys(marketOdds).length > 0);\n    // Debug: Log das odds para entender a estrutura\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PositionsTable.useEffect\": ()=>{\n            if (displayData.odds) {\n                console.log('🎲 Odds disponíveis:', displayData.odds);\n                console.log('🎯 Mercado ativo:', activeMarket);\n                console.log('📊 Odds do mercado:', marketOdds);\n            }\n        }\n    }[\"PositionsTable.useEffect\"], [\n        displayData.odds,\n        activeMarket,\n        marketOdds\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background flex flex-col h-full \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(isCollapsed ? 'flex-shrink-0' : 'flex-1', \" m-2 mx-4 border rounded-sm overflow-auto dark:bg-gradient-to-b from-neutral-800/40 via-neutral-900/20 to-transparent custom-scrollbar\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-1 border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between min-h-[32px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex  items-center flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start basis-[10%]\",\n                                        children: (displayData.isLive || isHalfTime || isExtraTime || displayData.finished) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(displayData.finished ? 'bg-gray-500' : isExtraTime ? 'bg-blue-500 animate-pulse' : isHalfTime ? 'bg-orange-500 animate-pulse' : 'bg-green-500 animate-pulse')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium whitespace-nowrap \".concat(displayData.finished ? 'text-gray-500' : isExtraTime ? 'text-blue-500' : isHalfTime ? 'text-orange-500' : 'text-green-500'),\n                                                    children: displayData.finished ? 'FINALIZADO' : isExtraTime ? 'TEMPO EXTRA' : isHalfTime ? 'INTERVALO' : \"\".concat(displayData.minute ? displayData.minute + \"'\" : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-mt:pl-1 flex w-full min-w-0 flex-col gap-1 pt-[2px] text-xs leading-[16px] min-mt:!flex-row min-mt:!gap-2 justify-center\",\n                                        children: [\n                                            renderGameTitleWithShields(),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: [\n                                                    (()=>{\n                                                        const filterGameInfo = (text)=>{\n                                                            if (!text) return false;\n                                                            const lowerText = text.toLowerCase();\n                                                            return !lowerText.includes('half') && !lowerText.includes('tempo') && !lowerText.includes('1st') && !lowerText.includes('2nd') && !lowerText.includes('1º') && !lowerText.includes('2º') && !lowerText.includes('primeiro') && !lowerText.includes('segundo') && !lowerText.includes('finished') && !lowerText.includes('extra time') && !lowerText.includes('postponed');\n                                                        };\n                                                        const competition = filterGameInfo(displayData.competition) ? displayData.competition : '';\n                                                        const country = filterGameInfo(displayData.country) ? displayData.country : '';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                competition,\n                                                                competition && country && ' • ',\n                                                                country\n                                                            ]\n                                                        }, void 0, true);\n                                                    })(),\n                                                    loading && ' • Carregando...',\n                                                    error && ' • Erro ao carregar'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>{},\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_DollarSign_Settings_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 70\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveMarket(market.id),\n                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(activeMarket === market.id ? \"bg-primary text-primary-foreground shadow-sm\" : \"bg-muted/20 text-muted-foreground hover:bg-muted/40 hover:text-foreground\"),\n                                        children: market.label\n                                    }, market.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: marketHeaders.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-3 font-medium text-muted-foreground\",\n                                                children: header\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hasOdds ? (()=>{\n                                        // Renderizar baseado na estrutura do mercado\n                                        if (activeMarket === \"1X2\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_x, _bookmaker_2;\n                                                const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n                                                const logoUrl = getBookmakerLogo(bookmakerName);\n                                                const initials = getBookmakerInitials(bookmakerName);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: logoUrl,\n                                                                        alt: bookmakerName,\n                                                                        width: 24,\n                                                                        height: 24,\n                                                                        className: \"rounded-sm\",\n                                                                        onError: (e)=>{\n                                                                            // Fallback para iniciais se logo não carregar\n                                                                            const target = e.target;\n                                                                            target.style.display = 'none';\n                                                                            const parent = target.parentElement;\n                                                                            if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                                                                const initialsDiv = document.createElement('div');\n                                                                                initialsDiv.className = 'bookmaker-initials w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center';\n                                                                                initialsDiv.textContent = initials;\n                                                                                parent.appendChild(initialsDiv);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 35\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\",\n                                                                        children: initials\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 35\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-foreground font-medium\",\n                                                                        children: bookmakerName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x = bookmaker.x) === null || _bookmaker_x === void 0 ? void 0 : _bookmaker_x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 27\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"OU\" && marketOdds) {\n                                            // Para Over/Under, temos diferentes linhas (0.5, 1.5, 2.5, etc.)\n                                            const lines = Object.keys(marketOdds).filter((key)=>key !== 'bookmakers' && key !== 'average' && key !== 'highest');\n                                            return lines.flatMap((line)=>{\n                                                const lineData = marketOdds[line];\n                                                if (!(lineData === null || lineData === void 0 ? void 0 : lineData.bookmakers)) return [];\n                                                return Object.entries(lineData.bookmakers).map((param)=>{\n                                                    let [bookmakerId, bookmaker] = param;\n                                                    var _bookmaker_over, _bookmaker_under;\n                                                    const bookmakerName = bookmaker.name || \"Casa \".concat(bookmakerId);\n                                                    const logoUrl = getBookmakerLogo(bookmakerName);\n                                                    const initials = getBookmakerInitials(bookmakerName);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        logoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: logoUrl,\n                                                                            alt: bookmakerName,\n                                                                            width: 20,\n                                                                            height: 20,\n                                                                            className: \"rounded-sm\",\n                                                                            onError: (e)=>{\n                                                                                const target = e.target;\n                                                                                target.style.display = 'none';\n                                                                                const parent = target.parentElement;\n                                                                                if (parent && !parent.querySelector('.bookmaker-initials')) {\n                                                                                    const initialsDiv = document.createElement('div');\n                                                                                    initialsDiv.className = 'bookmaker-initials w-5 h-5 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center';\n                                                                                    initialsDiv.textContent = initials;\n                                                                                    parent.appendChild(initialsDiv);\n                                                                                }\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 37\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-primary/20 text-primary text-xs font-bold rounded-sm flex items-center justify-center\",\n                                                                            children: initials\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-foreground font-medium text-sm\",\n                                                                            children: bookmakerName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3 text-muted-foreground font-medium\",\n                                                                children: line\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_over = bookmaker.over) === null || _bookmaker_over === void 0 ? void 0 : _bookmaker_over.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                    children: ((_bookmaker_under = bookmaker.under) === null || _bookmaker_under === void 0 ? void 0 : _bookmaker_under.current) || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(line, \"-\").concat(bookmakerId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 29\n                                                    }, this);\n                                                });\n                                            });\n                                        }\n                                        if (activeMarket === \"DC\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1x, _bookmaker_12, _bookmaker_x2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1x = bookmaker[\"1x\"]) === null || _bookmaker_1x === void 0 ? void 0 : _bookmaker_1x.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_12 = bookmaker[\"12\"]) === null || _bookmaker_12 === void 0 ? void 0 : _bookmaker_12.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_x2 = bookmaker.x2) === null || _bookmaker_x2 === void 0 ? void 0 : _bookmaker_x2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"DNB\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_1, _bookmaker_2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_1 = bookmaker[\"1\"]) === null || _bookmaker_1 === void 0 ? void 0 : _bookmaker_1.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_2 = bookmaker[\"2\"]) === null || _bookmaker_2 === void 0 ? void 0 : _bookmaker_2.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        if (activeMarket === \"BTTS\" && (marketOdds === null || marketOdds === void 0 ? void 0 : marketOdds.bookmakers)) {\n                                            return Object.entries(marketOdds.bookmakers).map((param)=>{\n                                                let [bookmakerId, bookmaker] = param;\n                                                var _bookmaker_yes, _bookmaker_no;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-border/50 hover:bg-gradient-to-r hover:from-neutral-800/20 hover:via-neutral-700/10 hover:to-transparent transition-all duration-200\",\n                                                    children: [\n                                                        renderBookmakerCell(bookmaker, bookmakerId),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_yes = bookmaker.yes) === null || _bookmaker_yes === void 0 ? void 0 : _bookmaker_yes.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center justify-center px-2 py-1 rounded bg-primary/10 text-primary font-medium min-w-[50px]\",\n                                                                children: ((_bookmaker_no = bookmaker.no) === null || _bookmaker_no === void 0 ? void 0 : _bookmaker_no.current) || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, bookmakerId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 25\n                                                }, this);\n                                            });\n                                        }\n                                        // Fallback para outros mercados\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: marketHeaders.length,\n                                                className: \"p-6 text-center text-muted-foreground\",\n                                                children: \"Estrutura de dados n\\xe3o suportada para este mercado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 23\n                                        }, this);\n                                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: marketHeaders.length,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: loading ? 'Carregando odds...' : 'Nenhuma odd disponível para este mercado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\positions-table.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(PositionsTable, \"MG6CjhWbHz2c8IePV/GGX36M08I=\");\n_c = PositionsTable;\nvar _c;\n$RefreshReg$(_c, \"PositionsTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/positions-table.tsx\n"));

/***/ })

});